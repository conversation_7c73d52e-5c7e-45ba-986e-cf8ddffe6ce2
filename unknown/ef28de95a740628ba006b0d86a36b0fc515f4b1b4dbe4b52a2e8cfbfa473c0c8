'use client';

import Image from 'next/image';
import { SvgIcon } from '@/components/common/ui/SvgIcon';
import { format } from 'date-fns';
import { useMarkersStore } from '@/stores/markers.store';
import { useEventsStore } from '@/stores/alert.store';

function DetailRow({ label, value }: { label: string; value: React.ReactNode }) {
    return (
        <div className="table-row text-xs">
            <span className="table-cell font-bold text-white whitespace-nowrap rtl:pl-2 ltr:pr-2 py-2">{label}</span>
            <span className="table-cell font-medium break-words whitespace-pre-wrap text-gray-400 py-2">{value}</span>
        </div>
    );
}

interface MarkerCardProps {
    markerId: string;
    outlineColor?: string;
    onClose?: () => void;
}

const MarkerCard: React.FC<MarkerCardProps> = ({ markerId, outlineColor = '#5C9DD5', onClose }) => {
    const marker = useMarkersStore((s) => s.markers.find((m) => m.id === Number(markerId)));

    // ✅ Directly use store selector instead of filtering manually
    const latestEvent = useEventsStore((s) => s.getLatestEventByMarkerId(markerId));

    if (!marker) return null;

    // Prefer event data if available
    const zone = latestEvent?.zone ?? marker.zone ?? '--';
    const description = latestEvent?.description ?? marker.description ?? '--';
    const status = latestEvent?.sourceState ?? marker.status ?? '-';
    const public_address = latestEvent?.message ?? marker.publicAddress ?? '-';

    const isPeople = marker.type === 'people' || marker.type === 'person';
    const isCamera = marker.type === 'camera';

    return (
        <div
            className="absolute -translate-x-1/2 -translate-y-full bg-gray-900 text-white rounded-lg shadow-xl min-w-[400px] z-[999] opacity-97"
            style={{ border: `2px solid ${outlineColor}` }}>
            {/* Header */}
            <div className="flex items-center justify-between p-3 border-b border-gray-700/70 opacity-90">
                <div className="flex items-center gap-2">
                    <div
                        className="rounded-full flex items-center justify-center"
                        style={{ backgroundColor: outlineColor, width: '40px', height: '40px', padding: '10px' }}>
                        <SvgIcon
                            name={marker.type}
                            size={isPeople ? 'sm' : 'default'}
                            strokeColor="black"
                            fillColor="black"
                            className={isPeople ? 'pb-10' : ''}
                        />
                    </div>
                    <div className="flex flex-col">
                        <h3 className="font-semibold text-sm">{marker.title ?? marker.type}</h3>
                        <p className="text-md text-gray-400">
                            {latestEvent?.name ?? '--'} –{' '}
                            {latestEvent?.datetime
                                ? format(new Date(latestEvent.datetime), 'MMM dd, yyyy HH:mm')
                                : '--'}
                        </p>
                    </div>
                </div>

                <div className="flex items-center gap-3">
                    <span
                        className="px-2 py-1 text-md font-medium rounded-sm "
                        style={{
                            color: outlineColor,
                            backgroundColor: `${outlineColor}55`,
                        }}>
                        {status}
                    </span>

                    <button onClick={onClose} className="text-gray-400 hover:text-white transition">
                        ✕
                    </button>
                </div>
            </div>

            {isCamera && (
                <div className="w-full h-48 overflow-hidden relative">
                    <Image
                        src="/meeting-room.jpg"
                        alt="Camera Stream"
                        fill
                        style={{ objectFit: 'cover' }}
                        className="rounded-lg"
                    />
                </div>
            )}

            <div className="px-3 py-2 opacity-90 border-b border-gray-700/70">
                {isPeople ? (
                    <div className="table w-full">
                        <DetailRow label="Source" value={marker.source ?? '-'} />
                        <DetailRow label="Zone" value={zone} />
                        <DetailRow label="Description" value={description} />
                        <DetailRow label="ID" value={marker.id} />
                    </div>
                ) : (
                    <div className="table w-full">
                        <DetailRow label="Zone" value={zone} />
                        <DetailRow label="Description" value={description} />
                        <DetailRow label="Message" value={public_address} />
                        <DetailRow label="ID" value={marker.id} />
                    </div>
                )}
            </div>

            {/* Footer */}
            {isPeople || isCamera ? (
                <div className="p-2 flex justify-between items-center">
                    {isPeople && (
                        <div
                            className="flex items-center rounded-xl px-2 py-1 border relative"
                            style={{ borderColor: '#5C9DD5' }}>
                            <div className="flex items-center gap-2">
                                <span
                                    className="flex items-center justify-center rounded-full w-6 h-6 text-white text-xs font-bold"
                                    style={{ backgroundColor: '#5C9DD5' }}>
                                    {latestEvent?.data?.detection_count ?? 0}
                                </span>

                                <span className="text-sm font-medium" style={{ color: '#5C9DD5' }}>
                                    Number of People
                                </span>
                            </div>
                        </div>
                    )}

                    {isCamera && (
                        <div className="flex gap-2">
                            <button
                                onClick={onClose}
                                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white text-sm rounded transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500">
                                Close
                            </button>
                            <button className="px-4 py-2 bg-blue-400 hover:bg-blue-700 text-white text-sm rounded transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500">
                                Watch Stream
                            </button>
                        </div>
                    )}
                </div>
            ) : (
                <div className="flex justify-end gap-2 p-2">
                    <button
                        onClick={onClose}
                        className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white text-sm rounded transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Close
                    </button>
                    <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500">
                        Action text
                    </button>
                </div>
            )}

            <div className="flex justify-center">
                <div className="w-4 h-4 bg-gray-900 rotate-45 translate-y-2"></div>
            </div>
        </div>
    );
};

export default MarkerCard;
