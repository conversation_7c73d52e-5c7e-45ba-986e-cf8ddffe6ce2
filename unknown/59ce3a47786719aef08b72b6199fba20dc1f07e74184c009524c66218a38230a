// System metric interface
export interface SystemMetric {
    key: string;
    value: string;
    isAlert: boolean;
    textAlign: 'start' | 'end';
}

// System entity interface
export interface SystemEntity {
    title: string;
    iconName: string;
    iconColor: string;
    metrics: SystemMetric[];
}

// API response interface
export interface GetSystemsApiResponse {
    systems: SystemEntity[];
}
