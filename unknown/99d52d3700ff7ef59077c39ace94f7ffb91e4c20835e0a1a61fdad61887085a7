//  Floor Entity
export interface Floor {
    id: number;
    name: string;
    code: string;
    level: number;
    description: string;
    displayName: string;
    isActive: boolean;
    hasAlerts: boolean;
    hasFloorPlan: boolean;
    totalRooms: number;
    totalDoors: number;
    deviceCount: number;
    totalMarkers: number;
    eventCount: number;
    building: {
        id: number;
        name: string;
        code: string;
    };
    zone: {
        id: number;
        name: string;
        code: string;
    };
    createdAt: string;
    updatedAt: string;
    buildingUrl: string;
    zonesUrl: string;
    roomsUrl: string;
    devicesUrl: string;
    markersUrl: string;
}

// Pagination
export interface Pagination {
    limit: number;
    offset: number;
    has_more: boolean;
}

// API Response Data
export interface FloorsData {
    floors: Floor[];
    total_count: number;
    returned_count: number;
    pagination: Pagination;
}

// Complete API response interface
export interface GetFloorsApiResponse {
    response_code: string;
    response_message: string;
    response_message_ar: string;
    data: FloorsData;
}

// Query parameters for filtering/pagination
export interface GetFloorsQueryParams {
    building_id?: number;
    zone_id?: number;
    level?: number;
    limit?: number;
    offset?: number;
    is_active?: boolean;
    search?: string;
}
