import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { System } from '@/infrastructure/api/systems/types';
import { mockSystems } from '@/infrastructure/api/systems/mock-systems';
import { systemService } from '@/infrastructure/api/systems/system.service';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type SystemsStoreType = SystemsState & SystemsActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------
interface SystemsState {
    systems: System[];
    isLoading: boolean;
    isMocked: boolean;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: SystemsState = {
    systems: [],
    isLoading: false,
    isMocked: false,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface SystemsActions {
    loadSystems: () => Promise<void>;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _systemsStore = (instanceId: string): StateCreator<SystemsStoreType> => {
    return (set): SystemsStoreType => ({
        ...DEFAULT_STATE,

        loadSystems: async () => {
            set((state: SystemsState) => ({ ...state, isLoading: true, isMocked: false }));

            try {
                // ✅ Try real API
                const res = await systemService.getSystems();

                set((state: SystemsState) => ({
                    ...state,
                    systems: res.systems,
                    isLoading: false,
                    isMocked: false,
                }));

                logger.info(
                    `systemsStore(${instanceId}): loadSystems: loaded ${res.systems.length} systems from API`,
                    res,
                );
            } catch (error) {
                // ❌ If API fails → fallback to mock
                logger.error(
                    `systemsStore(${instanceId}): loadSystems: API error, falling back to mock`,
                    error as Error,
                );

                const res = mockSystems;

                set((state: SystemsState) => ({
                    ...state,
                    systems: res.systems,
                    isLoading: false,
                    isMocked: true,
                }));

                logger.info(
                    `systemsStore(${instanceId}): loadSystems: loaded ${res.systems.length} systems from mock data`,
                    res,
                );
            }
        },
    });
};

// ----------------------------------------------------------
// ---------------------- store instances -------------------
// ----------------------------------------------------------
export const useSystemsStore = create<SystemsStoreType>()(
    subscribeWithSelector(devtools(_systemsStore('global'), { name: 'systems-store-global' })),
);

export const createSystemsStore = (instanceId: string) =>
    create<SystemsStoreType>()(
        subscribeWithSelector(devtools(_systemsStore(instanceId), { name: `systems-store-${instanceId}` })),
    );
