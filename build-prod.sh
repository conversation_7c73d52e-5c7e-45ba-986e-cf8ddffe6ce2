#!/bin/bash

# Production build script for Nebular System
# This script ensures environment variables are properly loaded before building

set -e

echo "🚀 Starting production build process..."

# Load environment variables from .env first (base variables)
if [ -f .env ]; then
    echo "📋 Loading base environment variables from .env..."
    export $(grep -v '^#' .env | grep -v '^$' | xargs)
    echo "✅ Base environment variables loaded"
fi

# Load environment variables from .env.production (override base variables)
if [ -f .env.production ]; then
    echo "📋 Loading production environment variables from .env.production..."
    export $(grep -v '^#' .env.production | grep -v '^$' | xargs)
    echo "✅ Production environment variables loaded"
    echo "🔗 API URL: $NEXT_PUBLIC_NEBULAR_API_BASE_URL"
else
    echo "❌ .env.production file not found!"
    exit 1
fi

# Verify critical environment variables are set
echo "🔍 Verifying environment variables:"
echo "NEXT_PUBLIC_NEBULAR_API_BASE_URL: ${NEXT_PUBLIC_NEBULAR_API_BASE_URL:-'NOT SET'}"
echo "NEXT_PUBLIC_API_BASE_URL: ${NEXT_PUBLIC_API_BASE_URL:-'NOT SET'}"
echo "NODE_ENV: ${NODE_ENV:-'NOT SET'}"

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose down nebular-prod 2>/dev/null || true

# Build the production image
echo "🔨 Building production Docker image..."
docker-compose build --no-cache nebular-prod

# Start the production container
echo "🚀 Starting production container..."
docker-compose up nebular-prod -d

# Wait for container to be ready
echo "⏳ Waiting for container to be ready..."
sleep 5

# Test the application
echo "🧪 Testing application..."
if curl -f -s http://localhost:4000 > /dev/null; then
    echo "✅ Application is running successfully!"
    echo "🌐 Access the application at: http://localhost:4000"
    echo "🗺️ Floor plan editor: http://localhost:4000/floor-plan-editor"
else
    echo "❌ Application failed to start properly"
    echo "📋 Container logs:"
    docker logs next_nublar_lc-nebular-prod-1 --tail 20
    exit 1
fi

echo "🎉 Production build completed successfully!"
