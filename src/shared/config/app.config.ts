// src/shared/config/app.config.ts

export interface AppConfig {
    api: {
        baseUrl: string;
        timeout: number;
        retryAttempts: number;
        retryDelay: number;
    };
    nebularApi: {
        // 👈 add this section back
        baseUrl: string;
        timeout: number;
        retryAttempts: number;
        retryDelay: number;
        enableMockData: boolean;
        autoRefresh: number;
    };
    features: {
        enableNewUI: boolean;
        enableLogging: boolean;
        enableDebugTools: boolean;
        useMarkerBasedMetrics: boolean; // Enable marker-based system metrics calculation
    };
    auth: {
        tokenStorageKey: string;
        refreshTokenKey: string;
        sessionTimeout: number;
    };
    i18n: {
        defaultLanguage: string;
        supportedLanguages: string[];
    };
    app: {
        name: string;
        version: string;
        environment: 'development' | 'staging' | 'production';
        buildDate: string;
    };
}

export class AppConfigManager {
    private config: AppConfig;
    private static instance: AppConfigManager;
    private listeners: Map<string, (config: AppConfig) => void> = new Map();

    private constructor() {
        this.config = this.loadConfiguration();
    }

    public static getInstance(): AppConfigManager {
        if (!AppConfigManager.instance) {
            AppConfigManager.instance = new AppConfigManager();
        }
        return AppConfigManager.instance;
    }

    // ✅ Add this
    public isDevelopment(): boolean {
        return this.config.app.environment === 'development';
    }

    // ✅ Add this
    public isProduction(): boolean {
        return this.config.app.environment === 'production';
    }

    private loadConfiguration(): AppConfig {
        // Safe environment variable access for client-side
        const getEnvVar = (key: string, defaultValue: string = '') => {
            // For NEXT_PUBLIC_ variables, Next.js automatically inlines these at build time
            // We can directly access process.env[key] as Next.js replaces it during build
            return process.env[key] || defaultValue;
        };

        // Use NODE_ENV for runtime environment
        const mode = getEnvVar('NODE_ENV', 'development');
        const isDevelopment = mode === 'development';
        // const isProduction = mode === "production";

        return {
            api: {
                baseUrl: getEnvVar('NEXT_PUBLIC_API_BASE_URL', 'http://localhost:3001/api'),
                timeout: parseInt(getEnvVar('NEXT_PUBLIC_API_TIMEOUT', '30000'), 10),
                retryAttempts: parseInt(getEnvVar('NEXT_PUBLIC_API_RETRY_ATTEMPTS', '3'), 10),
                retryDelay: parseInt(getEnvVar('NEXT_PUBLIC_API_RETRY_DELAY', '1000'), 10),
            },

            nebularApi: {
                baseUrl: getEnvVar('NEXT_PUBLIC_NEBULAR_API_BASE_URL', 'https://odoo.nebularhub.ai'),
                timeout: parseInt(getEnvVar('NEXT_PUBLIC_NEBULAR_API_TIMEOUT', '30000'), 10),
                retryAttempts: parseInt(getEnvVar('NEXT_PUBLIC_NEBULAR_API_RETRY_ATTEMPTS', '3'), 10),
                retryDelay: parseInt(getEnvVar('NEXT_PUBLIC_NEBULAR_API_RETRY_DELAY', '1000'), 10),
                enableMockData: getEnvVar('NEXT_PUBLIC_ENABLE_MOCK_DATA') === 'true',
                autoRefresh: parseInt(getEnvVar('NEXT_PUBLIC_NEBULAR_AUTO_REFRESH', '10000'), 10),
            },

            features: {
                enableNewUI: getEnvVar('NEXT_PUBLIC_ENABLE_NEW_UI') === 'true',
                enableLogging: getEnvVar('NEXT_PUBLIC_ENABLE_LOGGING') === 'true' || isDevelopment,
                enableDebugTools: getEnvVar('NEXT_PUBLIC_ENABLE_DEBUG_TOOLS') === 'true' && isDevelopment,
                useMarkerBasedMetrics: getEnvVar('NEXT_PUBLIC_USE_MARKER_BASED_METRICS') === 'true' || isDevelopment,
            },
            auth: {
                tokenStorageKey: getEnvVar('NEXT_PUBLIC_TOKEN_STORAGE_KEY', 'app_token'),
                refreshTokenKey: getEnvVar('NEXT_PUBLIC_REFRESH_TOKEN_KEY', 'app_refresh_token'),
                sessionTimeout: parseInt(getEnvVar('NEXT_PUBLIC_SESSION_TIMEOUT', '3600'), 10), // 1 hour
            },
            i18n: {
                defaultLanguage: getEnvVar('NEXT_PUBLIC_DEFAULT_LANGUAGE', 'en'),
                supportedLanguages: (getEnvVar('NEXT_PUBLIC_SUPPORTED_LANGUAGES', 'en,ar,fr')).split(','),
            },
            app: {
                name: getEnvVar('NEXT_PUBLIC_APP_NAME', 'Next.js App'),
                version: getEnvVar('NEXT_PUBLIC_APP_VERSION', '1.0.0'),
                environment: (mode as 'development' | 'staging' | 'production') || 'development',
                buildDate: getEnvVar('NEXT_PUBLIC_BUILD_DATE', new Date().toISOString()),
            },
        };
    }

    public getConfig(): AppConfig {
        return this.config;
    }

    public updateConfig(newConfig: Partial<AppConfig>, source: string): void {
        this.config = {
            ...this.config,
            ...newConfig,
            api: {
                ...this.config.api,
                ...(newConfig.api ?? {}),
            },
            features: {
                ...this.config.features,
                ...(newConfig.features ?? {}),
            },
            auth: {
                ...this.config.auth,
                ...(newConfig.auth ?? {}),
            },
            i18n: {
                ...this.config.i18n,
                ...(newConfig.i18n ?? {}),
            },
            app: {
                ...this.config.app,
                ...(newConfig.app ?? {}),
            },
        };

        const listener = this.listeners.get(source);
        if (listener) {
            listener(this.config);
        }
    }

    public subscribe(source: string, callback: (config: AppConfig) => void): void {
        this.listeners.set(source, callback);
    }

    public unsubscribe(source: string): void {
        this.listeners.delete(source);
    }
}

// ✅ Export a single shared instance
export const appConfig = AppConfigManager.getInstance();
export const config = appConfig.getConfig();
