/**
 * Nebular Event Management System API Types
 *
 * TypeScript interfaces for the Nebular Event Management System API
 * Based on API Documentation v1.0
 */

// System Codes
export type SystemCode = 'fire' | 'access' | 'cctv' | 'gate' | 'pa' | 'presence';

// Event Types by System
export type FireEventType = 'alarm' | 'fault' | 'test' | 'restore';
export type AccessEventType = 'entry' | 'exit' | 'denied' | 'forced';
export type CctvEventType = 'motion' | 'recording' | 'offline' | 'online';
export type GateEventType = 'open' | 'close' | 'vehicle_detected' | 'fault';
export type PaEventType = 'announcement' | 'emergency' | 'test' | 'fault';
export type PresenceEventType = 'occupied' | 'vacant' | 'motion' | 'fault';

export type EventType =
    | FireEventType
    | AccessEventType
    | CctvEventType
    | GateEventType
    | PaEventType
    | PresenceEventType;

// Base API Response Interface
export interface BaseApiResponse {
    response_code: string;
    response_message: string;
    response_message_ar?: string;
    error_message?: string;
}

// Fire Alarm System Data
export interface FireAlarmData {
    panelId: number;
    panelCode: string;
    panelName: string;
    zone: string;
    loop: string;
    nodeId: number;
    nodeCode: string;
    address: string;
}

// Access Control System Data
export interface AccessControlData {
    controllerId: number;
    controllerCode: string;
    controllerName: string;
    readerId: number;
    readerCode: string;
    cardId: number;
    cardCode: string;
    userId: number;
    userCode: string;
    doorId: string;
    doorName: string;
    result: 'granted' | 'denied' | 'timeout';
    reason: string;
    heldOpenSeconds: number;
}

// CCTV System Data
export interface CctvData {
    cameraId: number;
    cameraCode: string;
    cameraName: string;
    location: string;
    ip: string;
    channel: number;
    analyticType: string;
    count: number;
    recording: boolean;
}

// Gate Barrier System Data
export interface GateBarrierData {
    gateId: number;
    gateCode: string;
    gateName: string;
    status: string;
    vehiclePlate: string;
    trigger: string;
    anprConfidence: number;
}

// Public Address System Data
export interface PublicAddressData {
    zoneId: number;
    zoneCode: string;
    zoneName: string;
    volume: number;
    announcementId: string;
    script: string;
    durationSec: number;
}

// Presence Detection System Data
export interface PresenceDetectionData {
    sensorId: number;
    sensorCode: string;
    sensorName: string;
    sensorType: string;
    occupancy: boolean;
    detection_count?: number;
}

// Union type for all system data
export type SystemData =
    | FireAlarmData
    | AccessControlData
    | CctvData
    | GateBarrierData
    | PublicAddressData
    | PresenceDetectionData;

// Base Event Structure
export interface BaseEvent {
    name: string;
    buildingId: number;
    buildingCode: string;
    floorId: number;
    floorCode: string;
    systemCode: SystemCode;
    systemName: string;
    eventType: EventType;
    datetime: string; // ISO-8601 format
    message: string;
    zone: string;
    description: string;
    sourceEventCode: string;
    sourceState: string;
    state: string;
    severity: string;
    // Data: SystemData;
    data: {
        detection_count?: number;
    };
}

// Event with ID (for responses)
export interface Event extends BaseEvent {
    eventId?: number;
}

// Create Event Request (without eventId)
export type CreateEventRequest = BaseEvent;

// Get Events Query Parameters
export interface GetEventsQueryParams {
    system_code?: SystemCode;
    event_type?: EventType;
    building_id?: number;
    floor_id?: number;
    date_from?: string; // ISO-8601 format
    date_to?: string; // ISO-8601 format
    limit?: number;
    offset?: number;
    search?: string;
}

// Get Events Response
export interface GetEventsResponse extends BaseApiResponse {
    total_count: number;
    events: Event[];
}

// Single Event Response
export type GetEventResponse = BaseApiResponse & Event;

// Create Event Response
export type CreateEventResponse = BaseApiResponse & Event;

// Fast Test Response
export type FastTestResponse = BaseApiResponse & Event;

// Error Response
export type ErrorResponse = BaseApiResponse;

// API Service Response Wrapper
export interface ApiResponse<T> {
    data: T;
    status: number;
    statusText: string;
    headers: Record<string, string>;
}

// System Information
export interface SystemInfo {
    code: SystemCode;
    name: string;
    eventTypes: EventType[];
}

// Available Systems
export const AVAILABLE_SYSTEMS: SystemInfo[] = [
    {
        code: 'fire',
        name: 'Fire Alarm System',
        eventTypes: ['alarm', 'fault', 'test', 'restore'],
    },
    {
        code: 'access',
        name: 'Access Control System',
        eventTypes: ['entry', 'exit', 'denied', 'forced'],
    },
    {
        code: 'cctv',
        name: 'CCTV System',
        eventTypes: ['motion', 'recording', 'offline', 'online'],
    },
    {
        code: 'gate',
        name: 'Gate Barrier System',
        eventTypes: ['open', 'close', 'vehicle_detected', 'fault'],
    },
    {
        code: 'pa',
        name: 'Public Address System',
        eventTypes: ['announcement', 'emergency', 'test', 'fault'],
    },
    {
        code: 'presence',
        name: 'Presence Detection System',
        eventTypes: ['occupied', 'vacant', 'motion', 'fault'],
    },
];

// Response Code Constants
export const RESPONSE_CODES = {
    SUCCESS: '000',
    GENERAL_ERROR: '100',
    VALIDATION_ERROR: '101',
    UNAUTHORIZED: '103',
    FORBIDDEN: '104',
    NOT_FOUND: '404',
    CUSTOM_ERROR: '5000',
} as const;

export type ResponseCode = (typeof RESPONSE_CODES)[keyof typeof RESPONSE_CODES];
