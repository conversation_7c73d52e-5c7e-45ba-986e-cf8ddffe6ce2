import { useEffect, useState, useCallback } from 'react';
import { appConfig, type AppConfig } from '../config/app.config';

// Define the types locally since they're not exported from app.config
type ConfigSection = keyof AppConfig;
type ConfigValue<T extends ConfigSection> = AppConfig[T];

/**
 * React hook for accessing and subscribing to application configuration
 *
 * @example
 * ```tsx
 * // Get entire config
 * const { config, isLoading } = useAppConfig();
 *
 * // Get specific section
 * const { config: apiConfig } = useAppConfig('api');
 *
 * // Get config with custom selector
 * const { config: baseUrl } = useAppConfig(config => config.api.baseUrl);
 * ```
 */
interface UseAppConfigReturn<T = AppConfig> {
    config: T;
    isLoading: boolean;
    updateConfig: (updates: Partial<AppConfig>) => void;
    updateSection: <S extends ConfigSection>(section: S, updates: Partial<ConfigValue<S>>) => void;
    isDevelopment: boolean;
    isProduction: boolean;
    isFeatureEnabled: (feature: keyof AppConfig['features']) => boolean;
}

export function useAppConfig(): UseAppConfigReturn<AppConfig>;
export function useAppConfig<T extends ConfigSection>(section: T): UseAppConfigReturn<ConfigValue<T>>;
export function useAppConfig<T>(selector: (config: AppConfig) => T): UseAppConfigReturn<T>;
export function useAppConfig<T extends ConfigSection>(
    sectionOrSelector?: T | ((config: AppConfig) => unknown),
): UseAppConfigReturn<unknown> {
    const [config, setConfig] = useState(() => {
        const fullConfig = appConfig.getConfig();

        if (!sectionOrSelector) {
            return fullConfig;
        }

        if (typeof sectionOrSelector === 'function') {
            return sectionOrSelector(fullConfig);
        }

        return appConfig.get(sectionOrSelector);
    });

    const [isLoading, setIsLoading] = useState(false);

    // Subscribe to config changes
    useEffect(() => {
        const subscriptionId = `useAppConfig-${Date.now()}-${Math.random()}`;

        const handleConfigChange = (newConfig: AppConfig) => {
            if (!sectionOrSelector) {
                setConfig(newConfig);
            } else if (typeof sectionOrSelector === 'function') {
                setConfig(sectionOrSelector(newConfig));
            } else {
                setConfig(appConfig.get(sectionOrSelector));
            }
        };

        appConfig.subscribe(subscriptionId, handleConfigChange);

        return () => {
            appConfig.unsubscribe(subscriptionId);
        };
    }, [sectionOrSelector]);

    // Update entire config
    const updateConfig = useCallback((updates: Partial<AppConfig>) => {
        setIsLoading(true);
        try {
            appConfig.update(updates);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // Update specific section
    const updateSection = useCallback(<S extends ConfigSection>(section: S, updates: Partial<ConfigValue<S>>) => {
        setIsLoading(true);
        try {
            appConfig.updateSection(section, updates);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // Convenience methods
    const isDevelopment = appConfig.isDevelopment();
    const isProduction = appConfig.isProduction();
    const isFeatureEnabled = useCallback(
        (feature: keyof AppConfig['features']) => appConfig.isFeatureEnabled(feature),
        [],
    );

    return {
        config,
        isLoading,
        updateConfig,
        updateSection,
        isDevelopment,
        isProduction,
        isFeatureEnabled,
    };
}

/**
 * Hook for accessing a specific configuration value using dot notation
 *
 * @example
 * ```tsx
 * const baseUrl = useConfigValue<string>('api.baseUrl');
 * const theme = useConfigValue<string>('ui.theme');
 * ```
 */
export function useConfigValue<T>(path: string): T | undefined {
    const [value, setValue] = useState<T | undefined>(() => appConfig.getValue<T>(path));

    useEffect(() => {
        const subscriptionId = `useConfigValue-${path}-${Date.now()}`;

        const handleConfigChange = () => {
            setValue(appConfig.getValue<T>(path));
        };

        appConfig.subscribe(subscriptionId, handleConfigChange);

        return () => {
            appConfig.unsubscribe(subscriptionId);
        };
    }, [path]);

    return value;
}

/**
 * Hook for checking if a feature is enabled
 *
 * @example
 * ```tsx
 * const isLoggingEnabled = useFeatureFlag('enableLogging');
 * const isAnalyticsEnabled = useFeatureFlag('enableAnalytics');
 * ```
 */
export function useFeatureFlag(feature: keyof AppConfig['features']): boolean {
    const [isEnabled, setIsEnabled] = useState(() => appConfig.isFeatureEnabled(feature));

    useEffect(() => {
        const subscriptionId = `useFeatureFlag-${feature}-${Date.now()}`;

        const handleConfigChange = (config: AppConfig) => {
            setIsEnabled(config.features[feature]);
        };

        appConfig.subscribe(subscriptionId, handleConfigChange);

        return () => {
            appConfig.unsubscribe(subscriptionId);
        };
    }, [feature]);

    return isEnabled;
}

/**
 * Hook for accessing environment-specific configuration
 *
 * @example
 * ```tsx
 * const envConfig = useEnvironmentConfig();
 * console.log(envConfig.logLevel); // 'debug' in development, 'error' in production
 * ```
 */
export function useEnvironmentConfig(): Record<string, unknown> {
    const [envConfig, setEnvConfig] = useState(() => appConfig.getEnvironmentConfig());

    useEffect(() => {
        const subscriptionId = `useEnvironmentConfig-${Date.now()}`;

        const handleConfigChange = () => {
            setEnvConfig(appConfig.getEnvironmentConfig());
        };

        appConfig.subscribe(subscriptionId, handleConfigChange);

        return () => {
            appConfig.unsubscribe(subscriptionId);
        };
    }, []);

    return envConfig;
}
