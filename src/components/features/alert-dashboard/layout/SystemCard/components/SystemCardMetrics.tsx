import React from 'react';
import { cn } from '@/shared/utils';
import { SystemCardMetricsProps } from '../system-card.types';
import MetricDisplay from '@/components/common/ui/MetricDisplay';

function SystemCardMetrics({ primaryMetric, className }: SystemCardMetricsProps) {
    return (
        <div className={cn('', className)}>
            {/* Primary Metric */}
            {primaryMetric.length > 0 && (
                <div className="flex items-center justify-between">
                    {primaryMetric.map((metric, index) => (
                        <MetricDisplay
                            key={index}
                            label={metric.label}
                            value={metric.value}
                            isAlert={metric.isAlert}
                            layout="vertical"
                            textAlign={metric.textAlign}
                        />
                    ))}
                </div>
            )}
        </div>
    );
}

export default SystemCardMetrics;
