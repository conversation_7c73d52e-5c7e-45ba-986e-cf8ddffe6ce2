import React from 'react';
import { SystemMetric } from '@/infrastructure/api/systems/system.types';

/**
 * System card header props interface
 */
export interface SystemCardHeaderProps {
    title: string;
    iconName?: string;
    iconColor?: string;
    icon?: React.ComponentType<{ className?: string }> | string;
    className?: string;
}

/**
 * System card metrics props interface
 */
export interface SystemCardMetricsProps {
    primaryMetric: SystemMetric[];
    className?: string;
}
