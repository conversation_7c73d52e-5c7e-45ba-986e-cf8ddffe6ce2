import type { <PERSON>a, StoryObj } from '@storybook/react';
import SystemCard from './SystemCard';

const meta: Meta<typeof SystemCard> = {
    title: 'Features/Alert Dashboard/SystemCard',
    component: SystemCard,
    parameters: {
        layout: 'padded',
        docs: {
            description: {
                component: 'A reusable system card component that displays system information with metrics and alerts.',
            },
        },
    },
    argTypes: {
        title: {
            control: 'text',
            description: 'The title of the system card',
        },
        iconName: {
            control: 'select',
            options: ['fire', 'door', 'camera', 'gateBarrier', 'building', 'bell'],
            description: 'SVG icon name from public/svg/',
        },
        iconColor: {
            control: 'color',
            description: 'Color for the icon',
        },
        metrics: {
            control: 'object',
            description: 'Array of metrics to display',
        },
    },
};

export default meta;
type Story = StoryObj<typeof SystemCard>;

// Fire Alarm System Story
export const FireAlarmSystem: Story = {
    args: {
        title: 'Fire Alarm System',
        code: 'fire',
        iconName: 'fire',
        iconColor: '#ff4444',
        metrics: [
            {
                key: 'fire_total_devices',
                label: 'Total Devices',
                value: '42',
                isAlert: false,
            },
            {
                key: 'fire_active_alarms',
                label: 'Active Alarms',
                value: '5',
                isAlert: true,
            },
        ],
    },
};

// Access Control System Story
export const AccessControlSystem: Story = {
    args: {
        title: 'Access Control',
        code: 'access',
        iconName: 'door',
        iconColor: '#4444ff',
        metrics: [
            {
                key: 'access_total_doors',
                label: 'Total Doors',
                value: '28',
                isAlert: false,
            },
            {
                key: 'access_open',
                label: 'Open',
                value: '12',
                isAlert: false,
            },
            {
                key: 'access_closed',
                label: 'Closed',
                value: '16',
                isAlert: false,
            },
        ],
    },
};

// CCTV System Story
export const CCTVSystem: Story = {
    args: {
        title: 'CCTV System',
        code: 'cctv',
        iconName: 'camera',
        iconColor: '#44ff44',
        metrics: [
            {
                key: 'cctv_total_cameras',
                label: 'Total Cameras',
                value: '156',
                isAlert: false,
            },
            {
                key: 'cctv_active_incidents',
                label: 'Active Incidents',
                value: '2',
                isAlert: true,
            },
        ],
    },
};

// Gate Barriers System Story
export const GateBarriersSystem: Story = {
    args: {
        title: 'Gate Barriers',
        code: 'gate',
        iconName: 'gateBarrier',
        iconColor: '#ffaa44',
        metrics: [
            {
                key: 'gate_total_barriers',
                label: 'Total Barriers',
                value: '8',
                isAlert: false,
            },
            {
                key: 'gate_unauthorized_attempts',
                label: 'Unauthorized Attempts',
                value: '3',
                isAlert: true,
            },
        ],
    },
};

// With Emoji Icons
export const WithEmojiIcons: Story = {
    args: {
        title: 'Fire Alarm System',
        code: 'fire',
        iconName: 'fire',
        iconColor: '#ff4444',
        metrics: [
            {
                key: 'fire_total_devices',
                label: 'Total Devices',
                value: '42',
                isAlert: false,
            },
            {
                key: 'fire_active_alarms',
                label: 'Active Alarms',
                value: '5',
                isAlert: true,
            },
        ],
    },
};

// Loading State
export const LoadingState: Story = {
    args: {
        title: 'Fire Alarm System',
        code: 'fire',
        iconName: 'fire',
        iconColor: '#ff4444',
        metrics: [
            {
                key: 'fire_total_devices',
                label: 'Total Devices',
                value: '42',
                isAlert: false,
            },
            {
                key: 'fire_active_alarms',
                label: 'Active Alarms',
                value: '5',
                isAlert: true,
            },
        ],
    },
};

// Interactive Card
export const InteractiveCard: Story = {
    args: {
        title: 'Fire Alarm System',
        code: 'fire',
        iconName: 'fire',
        iconColor: '#ff4444',
        metrics: [
            {
                key: 'fire_total_devices',
                label: 'Total Devices',
                value: '42',
                isAlert: false,
            },
            {
                key: 'fire_active_alarms',
                label: 'Active Alarms',
                value: '5',
                isAlert: true,
            },
        ],
    },
};

// All Systems Grid - Mixed Icons (SVG + Emoji)
export const AllSystemsGrid: Story = {
    render: () => (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-6xl">
            <SystemCard
                title="Fire Alarm System"
                code="fire"
                iconName="fire"
                iconColor="#ff4444"
                metrics={[
                    { key: 'fire_total_devices', label: 'Total Devices', value: '42', isAlert: false },
                    { key: 'fire_active_alarms', label: 'Active Alarms', value: '5', isAlert: true },
                ]}
            />
            <SystemCard
                title="Access Control"
                code="access"
                iconName="door"
                iconColor="#4444ff"
                metrics={[
                    { key: 'access_total_doors', label: 'Total Doors', value: '28', isAlert: false },
                    { key: 'access_open', label: 'Open', value: '12', isAlert: false },
                    { key: 'access_closed', label: 'Closed', value: '16', isAlert: false },
                ]}
            />
            <SystemCard
                title="CCTV System"
                code="cctv"
                iconName="camera"
                iconColor="#44ff44"
                metrics={[
                    { key: 'cctv_total_cameras', label: 'Total Cameras', value: '156', isAlert: false },
                    { key: 'cctv_active_incidents', label: 'Active Incidents', value: '2', isAlert: true },
                ]}
            />
            <SystemCard
                title="Gate Barriers"
                code="gate"
                iconName="gateBarrier"
                iconColor="#ffaa44"
                metrics={[
                    { key: 'gate_total_barriers', label: 'Total Barriers', value: '8', isAlert: false },
                    { key: 'gate_unauthorized_attempts', label: 'Unauthorized Attempts', value: '3', isAlert: true },
                ]}
            />
        </div>
    ),
};

// Building Management System - Additional Example
export const BuildingManagement: Story = {
    args: {
        title: 'Building Management',
        code: 'building',
        iconName: 'building',
        iconColor: '#aa44ff',
        metrics: [
            {
                key: 'building_total_floors',
                label: 'Total Floors',
                value: '25',
                isAlert: false,
            },
            {
                key: 'building_occupied',
                label: 'Occupied',
                value: '23',
                isAlert: false,
            },
            {
                key: 'building_maintenance',
                label: 'Maintenance',
                value: '2',
                isAlert: true,
            },
        ],
    },
};
