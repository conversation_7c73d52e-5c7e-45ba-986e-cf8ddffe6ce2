/* eslint-disable react-hooks/exhaustive-deps */
'use client';

import React, { useEffect } from 'react';
import { useSystemsStore } from '@/stores/system.store';
import { useMarkersStore } from '@/stores/markers.store';
import { useFloorStore } from '@/stores/floor.store';
import { config } from '@/shared/config/app.config';
import { mergeSystemsWithMarkerMetrics } from '@/infrastructure/api/systems/marker-based-systems';
import { SystemEntity } from '@/infrastructure/api/systems/system.types';
import SystemCard from '../SystemCard/SystemCard';
import { cn } from '@/shared/utils';
import FloorAlertList from '@/components/features/floor-alert/FloorAlertList';
import { BuildingDropdown } from '@/components/common/BuildingDropdown';

interface SystemOverviewProps {
    className?: string;
}

/**
 * SystemOverview Component
 * Displays all systems using SystemCard components
 * Data is fetched from the systems store
 */
function SystemOverview({ className }: SystemOverviewProps) {
    const { systems, loadSystems } = useSystemsStore();
    const { getSystemMetricsFromMarkers, subscribeToFloorStore, markers } = useMarkersStore();
    const { selectedFloorId } = useFloorStore();

    // Determine which systems to display based on configuration
    const displaySystems = React.useMemo(() => {
        if (config.features.useMarkerBasedMetrics && selectedFloorId !== null) {
            console.log('SystemOverview: Merging systems with marker metrics');
            const markerMetrics = getSystemMetricsFromMarkers();
            return mergeSystemsWithMarkerMetrics(systems, markerMetrics);
        }
        return systems;
    }, [systems, selectedFloorId, getSystemMetricsFromMarkers, markers]);

    // Event handlers
    const handleBuildingChange = (building: string) => {
        console.log('Selected building:', building);
    };

    const handleFloorSelect = (floorId: string) => {
        console.log('Selected floor:', floorId);
    };

    // Load systems data on component mount
    useEffect(() => {
        loadSystems();
    }, [loadSystems]);

    // Initialize floor store subscription to reload markers when floor changes
    useEffect(() => {
        subscribeToFloorStore();
    }, [subscribeToFloorStore]);

    // if (isLoading) {
    //     return (
    //         <div className={cn('flex items-center justify-center p-8', className)}>
    //             <div className="text-gray-400">Loading systems...</div>
    //         </div>
    //     );
    // }

    if (displaySystems.length === 0) {
        return (
            <div className={cn('flex items-center justify-center p-8', className)}>
                <div className="text-gray-400">No systems available</div>
            </div>
        );
    }

    return (
        <div className={cn('flex flex-col h-full', className)}>
            {/* Systems Section */}
            <div className="flex-2 flex flex-col min-h-0">
                <div className="text-white font-poppins font-bold text-[18px] leading-[100%] mb-3">System Overview</div>
                <div className="flex-1 overflow-y-auto space-y-2 mb-6 pr-2">
                    {displaySystems.map((system: SystemEntity, index: number) => (
                        <SystemCard
                            key={`${system.title}-${index}`}
                            title={system.title}
                            iconName={system.iconName}
                            iconColor={system.iconColor}
                            metrics={system.metrics}
                        />
                    ))}
                </div>
            </div>

            {/* Floor List Section */}
            <div className="flex-1 flex flex-col min-h-0">
                {/* Building Selection Header and Dropdown on same row */}
                <div className="flex items-center justify-between mb-3">
                    <div className="text-white font-poppins font-semibold text-[18px] leading-none">Floors Alerts</div>
                    <BuildingDropdown />
                </div>

                {/* Floor List with Scroll */}
                <div className="flex-1 overflow-y-auto pr-2">
                    <FloorAlertList onBuildingChange={handleBuildingChange} onFloorSelect={handleFloorSelect} />
                </div>
            </div>
        </div>
    );
}

export default SystemOverview;
