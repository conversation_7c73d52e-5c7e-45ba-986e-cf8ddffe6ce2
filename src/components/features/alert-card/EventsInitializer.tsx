// app/EventsInitializer.tsx
'use client';

import { useEffect } from 'react';
import { useEventsStore } from '@/stores/alert.store';

export function EventsInitializer() {
    const startAutoRefresh = useEventsStore((s) => s.startAutoRefresh);
    const stopAutoRefresh = useEventsStore((s) => s.stopAutoRefresh);

    useEffect(() => {
        console.log('🔄 EventsInitializer: Starting auto refresh...');
        startAutoRefresh();
        return () => {
            console.log('🛑 EventsInitializer: Stopping auto refresh...');
            stopAutoRefresh();
        };
    }, [startAutoRefresh, stopAutoRefresh]);

    console.log('📱 EventsInitializer: Component rendered');
    return null; // nothing to render
}
