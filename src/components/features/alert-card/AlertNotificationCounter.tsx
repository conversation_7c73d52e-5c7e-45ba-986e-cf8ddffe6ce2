import { useEffect, useRef, useState } from 'react';
import { useEventsStore } from '@/stores/alert.store';
import { SvgIcon } from '@/components/common/ui/SvgIcon';
import AlertSliderNavigation from './AlertSliderNavigation';

export function AlertNotificationCounter() {
    const [open, setOpen] = useState(false);
    const cardRef = useRef<HTMLDivElement>(null);

    const totalAlertsInBuilding = useEventsStore((s) => s.totalAlertsInBuilding);
    const loadAlertEvents = useEventsStore((s) => s.loadAlertEvents);

    useEffect(() => {
        // Load initial alerts if not already loaded
        loadAlertEvents();
    }, [loadAlertEvents]);

    const hasAlerts = totalAlertsInBuilding > 0;
    const borderColor = hasAlerts ? '#EC4747' : '#5C9DD5';
    const textColor = hasAlerts ? '#EC4747' : '#5C9DD5';

    return (
        <>
            <div
                ref={cardRef}
                className="flex items-center justify-between rounded-xl px-2 py-1 border relative bg-[#0d131ff2]"
                style={{ borderColor }}>
                <div className="flex items-center gap-2">
                    <span
                        className="flex items-center justify-center rounded-full w-6 h-6 text-white text-xs font-bold"
                        style={{ backgroundColor: borderColor }}>
                        {totalAlertsInBuilding}
                    </span>
                    <span className="text-sm font-medium" style={{ color: textColor }}>
                        Alerts
                    </span>
                </div>

                <div className="px-2 cursor-pointer" onClick={() => setOpen(true)}>
                    <SvgIcon name="maximizeScreen" size="default" strokeColor="white" />
                </div>
            </div>

            {open && (
                <div className="fixed inset-0 bg-black/50 z-50" onClick={() => setOpen(false)}>
                    <div
                        className="absolute"
                        style={{
                            top: (cardRef.current?.getBoundingClientRect().bottom ?? 0) + 8,
                            left: cardRef.current?.getBoundingClientRect().left ?? 0,
                        }}>
                        <AlertSliderNavigation onClose={() => setOpen(false)} />
                    </div>
                </div>
            )}
        </>
    );
}
