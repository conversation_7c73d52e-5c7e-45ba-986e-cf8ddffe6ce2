'use client';

import React from 'react';
import { PlacedMarker } from './types';
import { SvgIcon } from '@/components/common/ui/SvgIcon';

interface CoordinateDisplayProps {
    currentCoordinates: { x: number; y: number } | null;
    placedMarkers: PlacedMarker[];
    onRemoveMarker: (markerId: string | number) => void;
    onToggleAlert: (markerId: string) => void;
    onExport: () => void;
    onClear: () => void;
}

export function CoordinateDisplay({
    currentCoordinates,
    placedMarkers,
    onRemoveMarker,
    onToggleAlert,
    onExport,
    onClear,
}: CoordinateDisplayProps) {
    const getMarkerSvgIcon = (type: string) => {
        switch (type) {
            case 'door':
                return 'door';
            case 'camera':
                return 'camera';
            case 'fire':
                return 'fire';
            case 'gate':
                return 'gateBarrier';
            default:
                return 'door';
        }
    };

    const getStatusColor = (status: string, isAlert: boolean) => {
        if (isAlert) return 'text-red-600';

        switch (status) {
            case 'normal':
                return 'text-green-600';
            case 'open':
                return 'text-blue-600';
            case 'closed':
                return 'text-gray-600';
            case 'recording':
                return 'text-green-600';
            default:
                return 'text-gray-600';
        }
    };

    return (
        <div className="h-full flex flex-col overflow-hidden">
            {/* Current Mouse Coordinates */}
            <div className="bg-gray-50 rounded-lg flex-shrink-0 mb-3 p-3">
                <h4 className="text-sm font-semibold text-gray-700 mb-1">Mouse Position</h4>
                {currentCoordinates ? (
                    <div className="text-sm text-gray-600">
                        <div>X: {Math.round(currentCoordinates.x)}px</div>
                        <div>Y: {Math.round(currentCoordinates.y)}px</div>
                    </div>
                ) : (
                    <div className="text-sm text-gray-400">Move mouse over floor plan</div>
                )}
            </div>

            {/* Placed Markers List */}
            <div className="flex-1 flex flex-col overflow-hidden min-h-0">
                <div className="flex items-center justify-between mb-2 flex-shrink-0">
                    <h4 className="text-sm font-semibold text-gray-700">Placed Markers ({placedMarkers.length})</h4>
                    <div className="flex space-x-2">
                        {placedMarkers.length > 0 && (
                            <>
                                <button
                                    onClick={onClear}
                                    className="px-3 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors">
                                    Clear All
                                </button>
                                <button
                                    onClick={onExport}
                                    className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                    Export JSON
                                </button>
                            </>
                        )}
                    </div>
                </div>

                {/* Scrollable Markers Container */}
                <div
                    className="flex-1 overflow-y-auto overflow-x-hidden border border-gray-200 rounded-lg"
                    style={{
                        minHeight: '300px',
                        height: 'calc(100vh - 250px)',
                    }}>
                    <div className="space-y-2 p-2">
                        {placedMarkers.length === 0 ? (
                            <div className="text-sm text-gray-400 text-center py-4">No markers placed yet</div>
                        ) : (
                            placedMarkers.map((marker) => (
                                <div
                                    key={marker.id}
                                    className={`p-3 bg-white border rounded-lg hover:border-gray-300 transition-all duration-300 ${
                                        marker.isAlert
                                            ? 'border-red-400 bg-red-50 shadow-lg ring-2 ring-red-200 ring-opacity-50'
                                            : 'border-gray-200'
                                    }`}
                                    style={
                                        marker.isAlert
                                            ? {
                                                  animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                                              }
                                            : {}
                                    }>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-2">
                                            <div className={`${marker.isAlert ? 'animate-bounce' : ''}`}>
                                                <SvgIcon
                                                    name={getMarkerSvgIcon(marker.type)}
                                                    size="sm"
                                                    strokeColor={marker.isAlert ? '#dc2626' : '#374151'}
                                                />
                                            </div>
                                            <div>
                                                <div className="text-sm font-medium text-gray-900">{marker.name}</div>
                                                <div
                                                    className={`text-xs ${getStatusColor(marker.status, marker.isAlert)}`}>
                                                    {marker.status
                                                        .replace('_', ' ')
                                                        .replace(/\b\w/g, (l) => l.toUpperCase())}
                                                    {marker.isAlert && (
                                                        <span className="ml-1 bg-red-100 text-red-600 px-2 py-0.5 rounded-full text-xs font-medium animate-pulse">
                                                            🚨 Alert
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-1">
                                            <button
                                                onClick={() => onToggleAlert(marker.id)}
                                                className={`w-6 h-6 flex items-center justify-center rounded-full text-xs font-bold transition-all duration-200 ${
                                                    marker.isAlert
                                                        ? 'bg-red-100 text-red-600 hover:bg-red-200 border border-red-300'
                                                        : 'bg-gray-100 text-gray-500 hover:bg-yellow-100 hover:text-yellow-600 border border-gray-300'
                                                }`}
                                                title={marker.isAlert ? 'Disable alert' : 'Enable alert'}>
                                                {marker.isAlert ? '🚨' : '⚠️'}
                                            </button>
                                            <button
                                                onClick={() => onRemoveMarker(marker.id)}
                                                className="w-6 h-6 flex items-center justify-center text-red-500 hover:text-red-700 hover:bg-red-100 rounded-full text-xs font-bold transition-colors"
                                                title="Remove marker">
                                                ×
                                            </button>
                                        </div>
                                    </div>

                                    <div className="mt-2 text-xs text-gray-500">
                                        <div>
                                            Position: {Math.round(marker.exact_x)}px, {Math.round(marker.exact_y)}px
                                        </div>
                                        <div>
                                            Building: {marker.buildingId} | Floor: {marker.floorId} | Zone:{' '}
                                            {marker.zoneId || 'N/A'}
                                        </div>
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
