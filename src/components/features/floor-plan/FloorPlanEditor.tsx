'use client';

import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { MarkerSelector } from './MarkerSelector';
import { PlacedMarkerComponent } from './PlacedMarkerComponent';
import { CoordinateDisplay } from './CoordinateDisplay';
import { MarkerType, PlacedMarker } from './types';
import { getBuildingById } from '@/infrastructure/api/geography/buildings/mock-buildings';
import { getZoneById } from '@/infrastructure/api/geography/zones/mock-zones';
import { getFloorById } from '@/infrastructure/api/geography/floors/mock-floors';

export function FloorPlanEditor() {
    const [selectedMarkerType, setSelectedMarkerType] = useState<MarkerType | null>(null);

    // Initialize with test markers to test scrolling
    const [placedMarkers, setPlacedMarkers] = useState<PlacedMarker[]>([
        {
            id: 'test-1',
            name: 'Door 1',
            type: 'door',
            status: 'normal',
            positionX: 25,
            positionY: 30,
            exact_x: 200,
            exact_y: 180,
            isAlert: false,
            floorId: '1',
            zoneId: 'zone-1',
            buildingId: '1',
            description: 'Test door marker',
            lastUpdated: new Date().toISOString(),
            metadata: { placedBy: 'test', placedAt: new Date().toISOString() }
        },
        {
            id: 'test-2',
            name: 'Camera 1',
            type: 'camera',
            status: 'recording',
            positionX: 50,
            positionY: 40,
            exact_x: 400,
            exact_y: 240,
            isAlert: false,
            floorId: '1',
            zoneId: 'zone-1',
            buildingId: '1',
            description: 'Test camera marker',
            lastUpdated: new Date().toISOString(),
            metadata: { placedBy: 'test', placedAt: new Date().toISOString() }
        },
        {
            id: 'test-3',
            name: 'Fire Detector 1',
            type: 'fire',
            status: 'normal',
            positionX: 75,
            positionY: 60,
            exact_x: 600,
            exact_y: 360,
            isAlert: true,
            floorId: '1',
            zoneId: 'zone-1',
            buildingId: '1',
            description: 'Test fire detector marker',
            lastUpdated: new Date().toISOString(),
            metadata: { placedBy: 'test', placedAt: new Date().toISOString() }
        },
        {
            id: 'test-4',
            name: 'Gate 1',
            type: 'gate',
            status: 'closed',
            positionX: 30,
            positionY: 70,
            exact_x: 240,
            exact_y: 420,
            isAlert: false,
            floorId: '1',
            zoneId: 'zone-1',
            buildingId: '1',
            description: 'Test gate marker',
            lastUpdated: new Date().toISOString(),
            metadata: { placedBy: 'test', placedAt: new Date().toISOString() }
        },
        {
            id: 'test-5',
            name: 'People Counter 1',
            type: 'people',
            status: 'normal',
            positionX: 60,
            positionY: 80,
            exact_x: 480,
            exact_y: 480,
            isAlert: false,
            floorId: '1',
            zoneId: 'zone-1',
            buildingId: '1',
            description: 'Test people counter marker',
            lastUpdated: new Date().toISOString(),
            metadata: { placedBy: 'test', placedAt: new Date().toISOString() }
        },
        {
            id: 'test-6',
            name: 'Speaker 1',
            type: 'volumeOn',
            status: 'normal',
            positionX: 80,
            positionY: 20,
            exact_x: 640,
            exact_y: 120,
            isAlert: false,
            floorId: '1',
            zoneId: 'zone-1',
            buildingId: '1',
            description: 'Test speaker marker',
            lastUpdated: new Date().toISOString(),
            metadata: { placedBy: 'test', placedAt: new Date().toISOString() }
        },
        {
            id: 'test-7',
            name: 'Door 2',
            type: 'door',
            status: 'open',
            positionX: 15,
            positionY: 50,
            exact_x: 120,
            exact_y: 300,
            isAlert: false,
            floorId: '1',
            zoneId: 'zone-1',
            buildingId: '1',
            description: 'Test door marker 2',
            lastUpdated: new Date().toISOString(),
            metadata: { placedBy: 'test', placedAt: new Date().toISOString() }
        },
        {
            id: 'test-8',
            name: 'Camera 2',
            type: 'camera',
            status: 'offline',
            positionX: 85,
            positionY: 45,
            exact_x: 680,
            exact_y: 270,
            isAlert: true,
            floorId: '1',
            zoneId: 'zone-1',
            buildingId: '1',
            description: 'Test camera marker 2',
            lastUpdated: new Date().toISOString(),
            metadata: { placedBy: 'test', placedAt: new Date().toISOString() }
        },
        {
            id: 'test-9',
            name: 'Fire Detector 2',
            type: 'fire',
            status: 'alarm_triggered',
            positionX: 40,
            positionY: 25,
            exact_x: 320,
            exact_y: 150,
            isAlert: true,
            floorId: '1',
            zoneId: 'zone-1',
            buildingId: '1',
            description: 'Test fire detector marker 2',
            lastUpdated: new Date().toISOString(),
            metadata: { placedBy: 'test', placedAt: new Date().toISOString() }
        },
        {
            id: 'test-10',
            name: 'Gate 2',
            type: 'gate',
            status: 'open',
            positionX: 70,
            positionY: 85,
            exact_x: 560,
            exact_y: 510,
            isAlert: false,
            floorId: '1',
            zoneId: 'zone-1',
            buildingId: '1',
            description: 'Test gate marker 2',
            lastUpdated: new Date().toISOString(),
            metadata: { placedBy: 'test', placedAt: new Date().toISOString() }
        }
    ]);

    const [currentCoordinates, setCurrentCoordinates] = useState<{ x: number; y: number } | null>(null);
    const [buildingId, setBuildingId] = useState<number>(1);
    const [floorId, setFloorId] = useState<number>(1);
    const [zoneId, setZoneId] = useState<number>(1);
    const [imageDimensions, setImageDimensions] = useState<{ width: number; height: number }>({ width: 800, height: 600 });
    const [zoomLevel, setZoomLevel] = useState<number>(1);
    const imageRef = useRef<HTMLImageElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    // Handle image load to get actual dimensions
    const handleImageLoad = (event: React.SyntheticEvent<HTMLImageElement>) => {
        const img = event.currentTarget;
        setImageDimensions({
            width: img.naturalWidth,
            height: img.naturalHeight,
        });
    };

    const handleImageClick = (event: React.MouseEvent<HTMLImageElement>) => {
        if (!selectedMarkerType || !imageRef.current) return;

        const rect = imageRef.current.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // Calculate percentage coordinates
        const percentageX = (x / rect.width) * 100;
        const percentageY = (y / rect.height) * 100;

        const newMarker: PlacedMarker = {
            id: Date.now().toString(),
            name: `${selectedMarkerType.charAt(0).toUpperCase() + selectedMarkerType.slice(1)} ${placedMarkers.length + 1}`,
            type: selectedMarkerType,
            positionX: percentageX,
            positionY: percentageY,
            exact_x: x,
            exact_y: y,
            status: 'normal',
            isAlert: false,
            floorId: floorId.toString(),
            zoneId: `zone-${zoneId}`,
            buildingId: buildingId.toString(),
            description: `${selectedMarkerType} marker placed at ${x.toFixed(0)}px, ${y.toFixed(0)}px`,
            lastUpdated: new Date().toISOString(),
            metadata: {
                placedBy: 'user',
                placedAt: new Date().toISOString(),
            },
        };

        setPlacedMarkers((prev) => [...prev, newMarker]);
    };

    const handleImageMouseMove = (event: React.MouseEvent<HTMLImageElement>) => {
        if (!imageRef.current) return;

        const rect = imageRef.current.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        setCurrentCoordinates({ x, y });
    };

    const handleImageMouseLeave = () => {
        setCurrentCoordinates(null);
    };

    const handleZoomIn = () => {
        setZoomLevel(prev => Math.min(prev + 0.25, 3));
    };

    const handleZoomOut = () => {
        setZoomLevel(prev => Math.max(prev - 0.25, 0.5));
    };

    const handleZoomReset = () => {
        setZoomLevel(1);
    };

    const removeMarker = (markerId: string | number) => {
        setPlacedMarkers((prev) => prev.filter((marker) => marker.id !== markerId));
    };

    const clearAllMarkers = () => {
        if (confirm('Are you sure you want to remove all markers?')) {
            setPlacedMarkers([]);
        }
    };

    const exportCoordinates = () => {
        // Get smart data from mock data
        const building = getBuildingById(buildingId);
        const zone = getZoneById(zoneId);
        const floor = getFloorById(floorId);

        const exportData = {
            markers: placedMarkers.map((marker, index) => {
                // Generate smart marker name based on type and index
                const markerTypePrefix =
                    marker.type === 'fire'
                        ? 'fire'
                        : marker.type === 'door'
                          ? 'door'
                          : marker.type === 'camera'
                            ? 'camera'
                            : marker.type === 'gate'
                              ? 'gate'
                              : marker.type === 'people'
                                ? 'people'
                                : marker.type === 'volumeOn'
                                  ? 'volumeOn'
                                  : marker.type;

                const markerNumber = String(index + 1).padStart(3, '0');
                const smartName = `${markerTypePrefix}-${markerNumber}`;

                // Generate smart title based on type
                const getSmartTitle = (type: string, name: string) => {
                    switch (type) {
                        case 'fire':
                            return `Fire Detector FD-${markerNumber}`;
                        case 'door':
                            return index === 0
                                ? 'Main Entrance'
                                : index === 1
                                  ? 'Executive Office Door'
                                  : index === 2
                                    ? 'Conference Room Door'
                                    : `Access Door ${name}`;
                        case 'camera':
                            return index === 0
                                ? 'Lobby Security Camera'
                                : index === 1
                                  ? 'Executive Area Camera'
                                  : index === 2
                                    ? 'Conference Room Camera'
                                    : `Security Camera ${name}`;
                        case 'gate':
                            return index === 0
                                ? 'Parking Gate PG-001'
                                : index === 1
                                  ? 'Security Turnstile GT-001'
                                  : `Security Gate ${name}`;
                        case 'people':
                            return index === 0
                                ? 'Reception Area People'
                                : index === 1
                                  ? 'Executive Area People'
                                  : index === 2
                                    ? 'Conference Room People'
                                    : `People Marker ${name}`;
                        case 'volumeOn':
                            return index === 0
                                ? 'Main PA Speaker'
                                : index === 1
                                  ? 'Emergency PA System'
                                  : index === 2
                                    ? 'Conference Room Audio'
                                    : `PA Speaker ${name}`;
                        default:
                            return `${type.charAt(0).toUpperCase() + type.slice(1)} ${name}`;
                    }
                };

                // Generate smart subtitle based on status
                const getSmartSubtitle = (type: string, status: string) => {
                    switch (status) {
                        case 'normal':
                            return 'Status: Normal';
                        case 'open':
                            return 'Access Granted';
                        case 'closed':
                            return 'Secure Access';
                        case 'locked':
                            return 'Access Denied';
                        case 'recording':
                            return 'HD Recording Active';
                        case 'offline':
                            return 'Connection Lost';
                        case 'motion_detected':
                            return 'Motion Alert';
                        case 'smoke_detected':
                            return 'Smoke Level: Critical';
                        default:
                            return `Status: ${status.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase())}`;
                    }
                };

                // Generate smart description
                const getSmartDescription = (type: string, status: string) => {
                    switch (type) {
                        case 'fire':
                            return status === 'smoke_detected'
                                ? 'Photoelectric smoke detector in north wing corridor'
                                : status === 'normal'
                                  ? 'Heat detector in north wing conference room'
                                  : 'Combination smoke and heat detector in executive area';
                        case 'door':
                            return index === 0
                                ? 'Main entrance door with card reader access'
                                : index === 1
                                  ? 'Executive office door with keycard access'
                                  : index === 2
                                    ? 'Conference room door with biometric scanner'
                                    : 'Automatic sliding door with proximity sensor in main lobby';
                        case 'camera':
                            return index === 0
                                ? 'Fixed security camera monitoring main lobby area'
                                : index === 1
                                  ? 'PTZ camera covering executive office area'
                                  : 'Conference room surveillance camera with motion detection';
                        case 'gate':
                            return index === 0
                                ? 'Automated parking barrier gate'
                                : 'Electronic turnstile gate for lobby access control';
                        case 'people':
                            return index === 0
                                ? 'People marker tracking occupancy in reception area'
                                : index === 1
                                  ? 'People marker monitoring executive area occupancy'
                                  : index === 2
                                    ? 'People marker tracking conference room attendance'
                                    : 'People marker for occupancy tracking and space utilization';
                        case 'volumeOn':
                            return index === 0
                                ? 'Main PA speaker for building-wide announcements'
                                : index === 1
                                  ? 'Emergency PA system for evacuation alerts'
                                  : index === 2
                                    ? 'Conference room audio system for presentations'
                                    : 'PA speaker for audio announcements and emergency alerts';
                        default:
                            return `${type} marker placed at ${Math.round(marker.exact_x)}px, ${Math.round(marker.exact_y)}px`;
                    }
                };

                // Generate smart zone description
                const getSmartZone = () => {
                    if (building && zone && floor) {
                        return `Floor ${floor.level} - ${building.shortCode} - ${zone.name}`;
                    }
                    return `Floor ${floorId} - Building ${buildingId} - Zone ${zoneId}`;
                };

                // Generate smart public address
                const getSmartPublicAddress = () => {
                    if (building && zone) {
                        const locationDetail =
                            index === 0
                                ? 'Main Lobby'
                                : index === 1
                                  ? 'Executive Office 101'
                                  : index === 2
                                    ? 'Conference Room Alpha'
                                    : 'Parking Entrance';
                        return `${building.name}, Ground Floor, ${zone.name}, ${locationDetail}`;
                    }
                    return `Building ${buildingId}, Floor ${floorId}, Zone ${zoneId}`;
                };

                return {
                    id: parseInt(marker.id) || index + 1,
                    name: smartName,
                    type: marker.type,
                    positionX: Math.round(marker.exact_x),
                    positionY: Math.round(marker.exact_y),
                    positionXPercent: Math.round(marker.positionX * 100) / 100,
                    positionYPercent: Math.round(marker.positionY * 100) / 100,
                    status: marker.status,
                    isAlert: marker.isAlert,
                    floorId: parseInt(marker.floorId),
                    zoneId: parseInt(marker.zoneId.replace('zone-', '')),
                    buildingId: parseInt(marker.buildingId),
                    title: getSmartTitle(marker.type, marker.name),
                    subtitle: getSmartSubtitle(marker.type, marker.status),
                    description: getSmartDescription(marker.type, marker.status),
                    zone: getSmartZone(),
                    publicAddress: getSmartPublicAddress(),
                    alertTimestamp: new Date().toISOString(),
                };
            }),
        };

        const jsonString = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const baseFilename = `b${buildingId}-f${floorId}.json`;
        
        const a = document.createElement('a');
        a.href = url;
        a.download = baseFilename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    const importCoordinates = () => {
        fileInputRef.current?.click();
    };

    const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        if (!file.name.endsWith('.json')) {
            alert('Please select a JSON file');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const content = e.target?.result as string;
                const data = JSON.parse(content);

                // Validate the JSON structure
                if (!data.markers || !Array.isArray(data.markers)) {
                    alert('Invalid JSON format. Expected structure: { "markers": [...] }');
                    return;
                }

                // Convert imported markers to PlacedMarker format
                const importedMarkers: PlacedMarker[] = data.markers.map((marker: {
                    id?: string | number;
                    name?: string;
                    type: string;
                    positionX: number;
                    positionY: number;
                    positionXPercent?: number;
                    positionYPercent?: number;
                    status?: string;
                    isAlert?: boolean;
                    floorId?: number | string;
                    zoneId?: number | string;
                    buildingId?: number | string;
                    title?: string;
                    subtitle?: string;
                    description?: string;
                    zone?: string;
                    publicAddress?: string;
                    alertTimestamp?: string;
                }, index: number) => {
                    // Validate required fields
                    if (!marker.type || typeof marker.positionX !== 'number' || typeof marker.positionY !== 'number') {
                        throw new Error(`Invalid marker at index ${index}: missing required fields`);
                    }

                    // Calculate percentage positions if not provided
                    const positionXPercent = marker.positionXPercent || (marker.positionX / 800) * 100;
                    const positionYPercent = marker.positionYPercent || (marker.positionY / 600) * 100;

                    return {
                        id: marker.id || `imported-${Date.now()}-${index}`,
                        name: marker.name || `${marker.type}-${index + 1}`,
                        type: marker.type as MarkerType,
                        positionX: positionXPercent,
                        positionY: positionYPercent,
                        exact_x: marker.positionX,
                        exact_y: marker.positionY,
                        status: marker.status || 'normal',
                        isAlert: marker.isAlert || false,
                        floorId: marker.floorId?.toString() || floorId.toString(),
                        zoneId: marker.zoneId ? `zone-${marker.zoneId}` : `zone-${zoneId}`,
                        buildingId: marker.buildingId?.toString() || buildingId.toString(),
                        metadata: {
                            title: marker.title || marker.name || `${marker.type} marker`,
                            subtitle: marker.subtitle || 'Imported marker',
                            description: marker.description || `Imported ${marker.type} marker`,
                            zone: marker.zone || `Floor ${floorId}`,
                            publicAddress: marker.publicAddress || `Building ${buildingId}`,
                            alertTimestamp: marker.alertTimestamp || new Date().toISOString(),
                            placedBy: 'import',
                            placedAt: new Date().toISOString(),
                        },
                    };
                });

                // Replace current markers with imported ones
                setPlacedMarkers(importedMarkers);
                alert(`Successfully imported ${importedMarkers.length} markers`);

            } catch (error) {
                console.error('Error importing JSON:', error);
                alert(`Error importing JSON: ${error instanceof Error ? error.message : 'Invalid file format'}`);
            }
        };

        reader.readAsText(file);
        // Reset the input value so the same file can be selected again
        event.target.value = '';
    };

    return (
        <div className="flex h-screen">
            {/* Left Panel - Compact Icon Toolbar */}
            <div className="w-20 bg-white border-r border-gray-200 p-2 flex flex-col items-center">
                <div className="mb-4">
                    <h3 className="text-xs font-semibold text-gray-600 mb-2 text-center">Tools</h3>
                    <MarkerSelector
                        selectedMarkerType={selectedMarkerType}
                        onMarkerTypeChange={setSelectedMarkerType}
                    />
                </div>

                {/* Compact Input Fields */}
                <div className="space-y-3">
                    <div className="flex flex-col items-center">
                        <label className="text-xs font-medium text-gray-700 mb-1">B</label>
                        <input
                            type="number"
                            min="1"
                            value={buildingId}
                            onChange={(e) => setBuildingId(parseInt(e.target.value) || 1)}
                            className="w-12 h-8 px-1 text-xs border border-gray-300 rounded text-center text-gray-900 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                    </div>
                    <div className="flex flex-col items-center">
                        <label className="text-xs font-medium text-gray-700 mb-1">F</label>
                        <input
                            type="number"
                            min="1"
                            value={floorId}
                            onChange={(e) => setFloorId(parseInt(e.target.value) || 1)}
                            className="w-12 h-8 px-1 text-xs border border-gray-300 rounded text-center text-gray-900 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                    </div>
                    <div className="flex flex-col items-center">
                        <label className="text-xs font-medium text-gray-700 mb-1">Z</label>
                        <input
                            type="number"
                            min="1"
                            value={zoneId}
                            onChange={(e) => setZoneId(parseInt(e.target.value) || 1)}
                            className="w-12 h-8 px-1 text-xs border border-gray-300 rounded text-center text-gray-900 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                    </div>
                </div>
            </div>

            {/* Right Panel - Floor Plan Editor */}
            <div className="flex-1 flex flex-col overflow-hidden">
                <div className="p-4 bg-white border-b">
                    <div className="flex justify-between items-center">
                        <div>
                            <h3 className="text-lg font-semibold text-gray-800">Floor Plan Editor</h3>
                        </div>
                        <div className="flex items-center gap-4">
                            <div className="text-sm text-gray-600">
                                Total Devices:{' '}
                                <span className="font-semibold text-gray-800">{placedMarkers.length}</span>
                            </div>

                            {/* Zoom Controls */}
                            <div className="flex items-center gap-2 border-l pl-4">
                                <span className="text-sm text-gray-600">Zoom:</span>
                                <button
                                    onClick={handleZoomOut}
                                    className="px-2 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
                                    disabled={zoomLevel <= 0.5}>
                                    −
                                </button>
                                <span className="text-sm text-gray-700 min-w-[3rem] text-center">
                                    {Math.round(zoomLevel * 100)}%
                                </span>
                                <button
                                    onClick={handleZoomIn}
                                    className="px-2 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
                                    disabled={zoomLevel >= 3}>
                                    +
                                </button>
                                <button
                                    onClick={handleZoomReset}
                                    className="px-2 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors">
                                    Reset
                                </button>
                            </div>

                            <div className="border-l pl-4 flex items-center gap-2">
                                <button
                                    onClick={() => setPlacedMarkers([])}
                                    className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors">
                                    Clear All
                                </button>
                                <button
                                    onClick={importCoordinates}
                                    className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
                                    Import JSON
                                </button>
                                <button
                                    onClick={exportCoordinates}
                                    className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                                    Export JSON
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Responsive Floor Plan Container */}
                <div
                    ref={containerRef}
                    className="flex-1 overflow-auto bg-gray-50 p-4"
                    style={{
                        scrollBehavior: 'smooth',
                    }}>
                    <div className="relative border-2 border-dashed border-gray-300 rounded-lg bg-white min-h-full flex items-center justify-center">
                        <div
                            className="relative inline-block"
                            style={{
                                transform: `scale(${zoomLevel})`,
                                transformOrigin: 'center center',
                                transition: 'transform 0.2s ease-in-out',
                            }}>
                            <Image
                                ref={imageRef}
                                src="/plans/floorPlan-b1-f1.png"
                                alt="Floor Plan"
                                width={imageDimensions.width}
                                height={imageDimensions.height}
                                className="w-auto h-auto object-contain cursor-crosshair"
                                onClick={handleImageClick}
                                onMouseMove={handleImageMouseMove}
                                onMouseLeave={handleImageMouseLeave}
                                onLoad={handleImageLoad}
                                priority
                                style={{
                                    minWidth: '400px',
                                    minHeight: '300px',
                                    maxWidth: 'none',
                                    maxHeight: 'none',
                                }}
                            />

                            {/* Render placed markers */}
                            {placedMarkers.map((marker) => (
                                <PlacedMarkerComponent
                                    key={marker.id}
                                    marker={marker}
                                    onRemove={() => removeMarker(marker.id)}
                                />
                            ))}
                        </div>
                    </div>
                </div>
            </div>

            {/* Bottom Panel - Coordinates Display */}
            <div className="w-80 bg-white border-l border-gray-200 flex flex-col h-full p-4">
                <CoordinateDisplay
                    currentCoordinates={currentCoordinates}
                    placedMarkers={placedMarkers}
                    onRemoveMarker={removeMarker}
                    onExport={exportCoordinates}
                    onClear={clearAllMarkers}
                />
            </div>

            {/* Hidden file input for JSON import */}
            <input
                ref={fileInputRef}
                type="file"
                accept=".json"
                onChange={handleFileImport}
                style={{ display: 'none' }}
            />
        </div>
    );
}
