'use client';

import { Stage, Layer, Image as KonvaImage } from 'react-konva';
import useImage from 'use-image';
import { useEffect, useState, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import Konva from 'konva';
import { useMarkersStore } from '@/stores/markers.store';
import { Marker } from './Marker';
import { Marker as MarkerType } from '@/infrastructure/api/markers';
import MarkerCard from './MarkerCard';

import { useEventsStore } from '@/stores/alert.store';

export interface FloorPlanViewerProps {
    className?: string;
    floorPlanUrl?: string;
    floorPlanAlt?: string;
    selectedFloor?: string;
    selectedBuilding?: string;
    onPlanClick?: (coordinates: { x: number; y: number }) => void;
    zoom?: number;
    onZoomChange?: (zoom: number) => void;
    panPosition?: { x: number; y: number };
    onPanChange?: (position: { x: number; y: number }) => void;
    isLoading?: boolean;
    error?: string;
}

export default function FloorPlan({
    className,
    floorPlanUrl = '/2dFloorPlan.png',
    zoom: zoomProp = 1,
    onZoomChange,
    error,
}: FloorPlanViewerProps) {
    const [plan] = useImage(floorPlanUrl);
    const { markers, isLoading: markersLoading } = useMarkersStore();
    const containerRef = useRef<HTMLDivElement>(null);
    const stageRef = useRef<Konva.Stage>(null);
    const [dimensions, setDimensions] = useState({ width: 0, height: 0, scale: 1 });
    const [selectedMarkerId, setSelectedMarkerId] = useState<string | null>(null);
    const [zoom, setZoom] = useState(zoomProp);
    const [isInitialLoad, setIsInitialLoad] = useState(true);
    const [isPinching, setIsPinching] = useState(false);
    // Subscribe to events so FloorPlan re-renders when events update
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const events = useEventsStore((s) => s.events);

    const getLatestEventByMarkerId = useEventsStore((s) => s.getLatestEventByMarkerId);

    // keep in sync with external zoom prop
    useEffect(() => {
        setZoom(zoomProp);
    }, [zoomProp]);

    // Center the floor plan in the container
    const centerFloorPlan = useCallback(() => {
        if (!containerRef.current || !plan) return;

        const container = containerRef.current;
        const containerWidth = container.clientWidth;
        const containerHeight = container.clientHeight;

        // Calculate the zoomed content size
        const zoomedWidth = dimensions.width * zoom;
        const zoomedHeight = dimensions.height * zoom;

        // Center the content
        const scrollLeft = Math.max(0, (zoomedWidth - containerWidth) / 2);
        const scrollTop = Math.max(0, (zoomedHeight - containerHeight) / 2);

        container.scrollTo({
            left: scrollLeft,
            top: scrollTop,
            behavior: isInitialLoad ? 'auto' : 'smooth',
        });

        if (isInitialLoad) {
            setIsInitialLoad(false);
        }
    }, [dimensions.width, dimensions.height, zoom, plan, isInitialLoad]);

    // Calculate responsive dimensions
    useEffect(() => {
        const calculateDimensions = () => {
            if (!plan || !containerRef.current) return;
            const container = containerRef.current;
            const containerWidth = container.clientWidth;
            const containerHeight = container.clientHeight;

            // Calculate base scale to fit the image in the container with better default zoom
            const scaleX = containerWidth / plan.width;
            const scaleY = containerHeight / plan.height;
            const scale = Math.min(scaleX, scaleY) * 0.8; // Smaller base scale for better default zoom

            setDimensions({
                width: plan.width * scale,
                height: plan.height * scale,
                scale,
            });

            // Set better default zoom on initial load
            if (isInitialLoad && zoomProp === 1) {
                const defaultZoom = Math.min(2.0, Math.max(1.2, 1 / scale)); // Default zoom between 1.2x and 2x
                setZoom(defaultZoom);
                onZoomChange?.(defaultZoom);
            }
        };

        calculateDimensions();
        window.addEventListener('resize', calculateDimensions);
        return () => window.removeEventListener('resize', calculateDimensions);
    }, [plan, isInitialLoad, zoomProp, onZoomChange]);

    // Center the floor plan when dimensions or zoom changes
    useEffect(() => {
        if (dimensions.width > 0 && dimensions.height > 0) {
            // Small delay to ensure DOM is updated
            const timeoutId = setTimeout(centerFloorPlan, 100);
            return () => clearTimeout(timeoutId);
        }
    }, [dimensions, zoom, centerFloorPlan]);

    useEffect(() => {
        const unsubscribe = useMarkersStore.getState().subscribeToFloorStore();
        return unsubscribe; // cleanup when unmounting
    }, []);

    // ✅ Improved wheel event handler that distinguishes between scroll and zoom
    const handleWheel = useCallback(
        (e: Konva.KonvaEventObject<WheelEvent>) => {
            const evt = e.evt;

            // Check if this is a pinch-to-zoom gesture (Ctrl/Cmd + wheel) or intentional zoom
            const isZoomGesture = evt.ctrlKey || evt.metaKey || Math.abs(evt.deltaY) > 50;

            // If it's not a zoom gesture, let the container handle scrolling
            if (!isZoomGesture) {
                return; // Don't prevent default, allow normal scrolling
            }

            // Prevent default only for zoom gestures
            evt.preventDefault();

            if (!containerRef.current || !stageRef.current) return;

            const container = containerRef.current;
            const stage = stageRef.current;

            // Get mouse position relative to the stage
            const pointer = stage.getPointerPosition();
            if (!pointer) return;

            // Get current scroll position
            const scrollLeft = container.scrollLeft;
            const scrollTop = container.scrollTop;

            // Calculate mouse position in the scrollable content
            const mouseX = pointer.x / zoom + scrollLeft;
            const mouseY = pointer.y / zoom + scrollTop;

            const delta = evt.deltaY > 0 ? -0.15 : 0.15; // Slightly faster zoom
            const newZoom = Math.min(Math.max(zoom + delta, 0.3), 4); // Extended zoom range

            setZoom(newZoom);
            onZoomChange?.(newZoom);

            // Calculate new scroll position to keep mouse position centered
            setTimeout(() => {
                const newScrollLeft = mouseX * newZoom - pointer.x;
                const newScrollTop = mouseY * newZoom - pointer.y;

                container.scrollTo({
                    left: Math.max(0, newScrollLeft),
                    top: Math.max(0, newScrollTop),
                    behavior: 'auto',
                });
            }, 0);
        },
        [zoom, onZoomChange],
    );

    // Touch event handlers for pinch-to-zoom
    const handleTouchStart = useCallback((e: React.TouchEvent) => {
        if (e.touches.length === 2) {
            setIsPinching(true);
            e.preventDefault();
        }
    }, []);

    const handleTouchMove = useCallback(
        (e: React.TouchEvent) => {
            if (e.touches.length === 2 && isPinching) {
                e.preventDefault();
                // Touch pinch-to-zoom logic would go here
                // For now, we'll rely on the browser's native pinch-to-zoom
            }
        },
        [isPinching],
    );

    const handleTouchEnd = useCallback(() => {
        setIsPinching(false);
    }, []);

    // Only show loading for critical errors or when floor plan image fails to load
    if (error) {
        return <div className="text-red-600">Error: {error}</div>;
    }

    // Don't unmount the entire component for markers loading - just render without markers
    // This prevents Konva animations from being destroyed and recreated

    const getMarkerStyle = (marker: MarkerType) => {
        const latestEvent = getLatestEventByMarkerId(String(marker.id));

        // Enhanced alert detection - check for multiple alert conditions
        const isAlert =
            latestEvent &&
            ((latestEvent.severity?.toLowerCase() === 'critical' &&
                !['resolved', 'closed'].includes(latestEvent.state?.toLowerCase() ?? '')) ||
                (latestEvent.severity?.toLowerCase() === 'high' &&
                    ['active', 'triggered', 'alarm'].includes(latestEvent.state?.toLowerCase() ?? '')) ||
                (marker.type === 'fire' && latestEvent.state?.toLowerCase() === 'alarm_triggered'));

        if (isAlert) {
            return {
                circleFill: '#EC4747',
                fileName: marker.type,
                isAlert: true,
            };
        }

        // Enhanced color scheme with better contrast
        switch (marker.type) {
            case 'door':
                return { circleFill: '#10BCAD', fileName: 'door', isAlert: false };
            case 'camera':
                return { circleFill: '#877BD7', fileName: 'camera', isAlert: false };
            case 'fire':
                return { circleFill: '#E87027', fileName: 'fire', isAlert: false };
            case 'gate':
                return { circleFill: '#5C9DD5', fileName: 'gate', isAlert: false };
            case 'people':
                return { circleFill: '#EAB324', fileName: 'people', isAlert: false };
            case 'person':
                return { circleFill: '#EAB324', fileName: 'person', isAlert: false };
            case 'volumeOn':
                return { circleFill: '#7ED321', fileName: 'volumeOn', isAlert: false };
            default:
                return { circleFill: '#9B9B9B', fileName: 'building', isAlert: false };
        }
    };

    // ✅ fixed sizes per type
    const cardSizes: Record<string, { width: number; height: number }> = {
        camera: { width: 450, height: 320 },
        default: { width: 400, height: 250 },
    };

    return (
        <div className={`w-full h-screen relative bg-gray-900 ${className ?? ''}`}>
            {/* Enhanced zoom controls - positioned within floor plan container */}
            <div className="absolute top-4 right-4 z-50">
                <div className="flex flex-col gap-2 bg-white/10 backdrop-blur-sm rounded-lg p-2 shadow-2xl border border-white/20 w-fit">
                    {/* Zoom In Button */}
                    <button
                        className="w-12 h-12 flex items-center justify-center bg-blue-600 hover:bg-blue-500 active:bg-blue-700 rounded-lg shadow-lg text-white transition-all duration-200 transform hover:scale-105"
                        onClick={() => {
                            const newZoom = Math.min(zoom + 0.3, 4);
                            setZoom(newZoom);
                            onZoomChange?.(newZoom);
                        }}
                        title="Zoom In (Ctrl + Scroll)">
                        <svg
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2.5">
                            <circle cx="11" cy="11" r="8" />
                            <path d="M21 21l-4.35-4.35" />
                            <line x1="8" y1="11" x2="14" y2="11" />
                            <line x1="11" y1="8" x2="11" y2="14" />
                        </svg>
                    </button>

                    {/* Zoom Level Display */}
                    <div className="px-2 py-1 bg-gray-800/80 rounded-md text-white text-xs font-medium text-center w-12">
                        {Math.round(zoom * 100)}%
                    </div>

                    {/* Zoom Out Button */}
                    <button
                        className="w-12 h-12 flex items-center justify-center bg-blue-600 hover:bg-blue-500 active:bg-blue-700 rounded-lg shadow-lg text-white transition-all duration-200 transform hover:scale-105"
                        onClick={() => {
                            const newZoom = Math.max(zoom - 0.3, 0.3);
                            setZoom(newZoom);
                            onZoomChange?.(newZoom);
                        }}
                        title="Zoom Out (Ctrl + Scroll)">
                        <svg
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2.5">
                            <circle cx="11" cy="11" r="8" />
                            <path d="M21 21l-4.35-4.35" />
                            <line x1="8" y1="11" x2="14" y2="11" />
                        </svg>
                    </button>

                    {/* Reset Button */}
                    <button
                        className="w-12 h-12 flex items-center justify-center bg-gray-600 hover:bg-gray-500 active:bg-gray-700 rounded-lg shadow-lg text-white transition-all duration-200 transform hover:scale-105"
                        onClick={() => {
                            const defaultZoom = Math.min(2.0, Math.max(1.2, 1 / dimensions.scale));
                            setZoom(defaultZoom);
                            onZoomChange?.(defaultZoom);
                            setTimeout(centerFloorPlan, 100);
                        }}
                        title="Reset Zoom & Center">
                        <svg
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                            <polyline points="9,22 9,12 15,12 15,22" />
                        </svg>
                    </button>
                </div>

                {/* Compact Instructions */}
                <div className="mt-2 bg-black/60 backdrop-blur-sm rounded-md p-2 text-white text-xs w-fit">
                    <div className="font-medium mb-1 text-center">Controls</div>
                    <div>• Ctrl+Scroll: Zoom</div>
                    <div>• Scroll: Pan</div>
                    <div>• Touch: Pinch</div>
                </div>
            </div>

            <div
                ref={containerRef}
                className="w-full h-full"
                style={{
                    overflow: 'auto',
                    scrollBehavior: 'smooth',
                    WebkitOverflowScrolling: 'touch', // Better touch scrolling on iOS
                }}
                onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}>
                {plan && dimensions.width > 0 && (
                    <div
                        style={{
                            width: Math.max(dimensions.width * zoom, containerRef.current?.clientWidth || 0),
                            height: Math.max(dimensions.height * zoom, containerRef.current?.clientHeight || 0),
                            position: 'relative',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            padding: '20px', // Add padding for better scrolling experience
                        }}>
                        <Stage
                            ref={stageRef}
                            width={dimensions.width * zoom}
                            height={dimensions.height * zoom}
                            scaleX={1}
                            scaleY={1}
                            onWheel={handleWheel}>
                            <Layer>
                                <KonvaImage
                                    image={plan}
                                    width={dimensions.width * zoom}
                                    height={dimensions.height * zoom}
                                />
                                {/* Only render markers when not loading to prevent animation interruption */}
                                {!markersLoading &&
                                    markers.map((m) => {
                                        const style = getMarkerStyle(m);
                                        const x =
                                            m.positionXPercent !== undefined
                                                ? (m.positionXPercent / 100) * dimensions.width * zoom
                                                : (m.positionX / plan.width) * dimensions.width * zoom;
                                        const y =
                                            m.positionYPercent !== undefined
                                                ? (m.positionYPercent / 100) * dimensions.height * zoom
                                                : (m.positionY / plan.height) * dimensions.height * zoom;

                                        return (
                                            <Marker
                                                key={m.id}
                                                x={x}
                                                y={y}
                                                fileName={style.fileName}
                                                radius={30 * zoom}
                                                circleFill={style.circleFill}
                                                circleStroke={style.circleFill}
                                                pathStroke="black"
                                                isAlert={style.isAlert}
                                                onClick={() => setSelectedMarkerId(m.id.toString())}
                                            />
                                        );
                                    })}
                            </Layer>
                        </Stage>

                        {/* MarkerCard via portal */}
                        {selectedMarkerId &&
                            (() => {
                                const marker = markers.find((m) => m.id.toString() === selectedMarkerId);
                                if (!marker || !containerRef.current) return null;

                                const style = getMarkerStyle(marker);

                                // ✅ marker coordinates in canvas with zoom applied
                                const markerLeft =
                                    (((marker.positionXPercent ?? 0) + 5.5) / 100) * dimensions.width * zoom;
                                const markerTop =
                                    (((marker.positionYPercent ?? 0) + 4.5) / 100) * dimensions.height * zoom;

                                // ✅ account for container scroll
                                const scrollLeft = containerRef.current.scrollLeft;
                                const scrollTop = containerRef.current.scrollTop;

                                // ✅ get container position
                                const containerRect = containerRef.current.getBoundingClientRect();

                                let left = containerRect.left + markerLeft - scrollLeft;
                                let top = containerRect.top + markerTop - scrollTop;

                                const { width: cardWidth, height: cardHeight } =
                                    cardSizes[marker.type] ?? cardSizes.default;
                                const padding = 16;

                                // clamp within viewport
                                left = Math.min(
                                    Math.max(left, padding + cardWidth / 2),
                                    window.innerWidth - padding - cardWidth / 2,
                                );

                                const placeAbove = top - cardHeight - padding > 0;
                                if (placeAbove) {
                                    top = Math.max(top, padding + cardHeight);
                                } else {
                                    top = Math.min(top + cardHeight, window.innerHeight - padding);
                                }

                                return createPortal(
                                    <div
                                        style={{
                                            position: 'absolute',
                                            left,
                                            top,
                                            transform: placeAbove ? 'translate(-50%, -100%)' : 'translate(-50%, 0)',
                                            zIndex: 9999,
                                            pointerEvents: 'auto',
                                        }}>
                                        <MarkerCard
                                            markerId={selectedMarkerId}
                                            outlineColor={style.circleFill}
                                            onClose={() => setSelectedMarkerId(null)}
                                        />
                                    </div>,
                                    document.body,
                                );
                            })()}
                    </div>
                )}
            </div>
        </div>
    );
}
