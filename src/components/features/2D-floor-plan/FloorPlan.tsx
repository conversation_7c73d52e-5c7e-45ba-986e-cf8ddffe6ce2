'use client';

import { Stage, Layer, Image as KonvaImage } from 'react-konva';
import useImage from 'use-image';
import { useEffect, useState, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import Konva from 'konva';
import { useMarkersStore } from '@/stores/markers.store';
import { Marker } from './Marker';
import { Marker as MarkerType } from '@/infrastructure/api/markers';
import MarkerCard from './MarkerCard';
import { SvgIcon } from '@/components/common';
import { useEventsStore } from '@/stores/alert.store';

export interface FloorPlanViewerProps {
    className?: string;
    floorPlanUrl?: string;
    floorPlanAlt?: string;
    selectedFloor?: string;
    selectedBuilding?: string;
    onPlanClick?: (coordinates: { x: number; y: number }) => void;
    zoom?: number;
    onZoomChange?: (zoom: number) => void;
    panPosition?: { x: number; y: number };
    onPanChange?: (position: { x: number; y: number }) => void;
    isLoading?: boolean;
    error?: string;
}

export default function FloorPlan({
    className,
    floorPlanUrl = '/2dFloorPlan.png',
    zoom: zoomProp = 1,
    onZoomChange,
    error,
}: FloorPlanViewerProps) {
    const [plan] = useImage(floorPlanUrl);
    const { markers, isLoading: markersLoading } = useMarkersStore();
    const containerRef = useRef<HTMLDivElement>(null);
    const [dimensions, setDimensions] = useState({ width: 0, height: 0, scale: 1 });
    const [selectedMarkerId, setSelectedMarkerId] = useState<string | null>(null);
    const [zoom, setZoom] = useState(zoomProp);
    // Subscribe to events so FloorPlan re-renders when events update
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const events = useEventsStore((s) => s.events);

    const getLatestEventByMarkerId = useEventsStore((s) => s.getLatestEventByMarkerId);

    // keep in sync with external zoom prop
    useEffect(() => {
        setZoom(zoomProp);
    }, [zoomProp]);

    // Calculate responsive dimensions
    useEffect(() => {
        const calculateDimensions = () => {
            if (!plan || !containerRef.current) return;
            const container = containerRef.current;
            const containerWidth = container.clientWidth;
            const containerHeight = container.clientHeight;

            const scaleX = containerWidth / plan.width;
            const scaleY = containerHeight / plan.height;
            const scale = Math.max(scaleX, scaleY * 0.8);

            setDimensions({
                width: plan.width * scale,
                height: plan.height * scale,
                scale,
            });
        };

        calculateDimensions();
        window.addEventListener('resize', calculateDimensions);
        return () => window.removeEventListener('resize', calculateDimensions);
    }, [plan]);

    useEffect(() => {
        const unsubscribe = useMarkersStore.getState().subscribeToFloorStore();
        return unsubscribe; // cleanup when unmounting
    }, []);

    // ✅ Typed Konva wheel event
    const handleWheel = useCallback(
        (e: Konva.KonvaEventObject<WheelEvent>) => {
            e.evt.preventDefault();
            const delta = e.evt.deltaY > 0 ? -0.1 : 0.1;
            const newZoom = Math.min(Math.max(zoom + delta, 0.5), 3); // clamp between 0.5–3
            setZoom(newZoom);
            onZoomChange?.(newZoom);
        },
        [zoom, onZoomChange],
    );

    // Only show loading for critical errors or when floor plan image fails to load
    if (error) {
        return <div className="text-red-600">Error: {error}</div>;
    }

    // Don't unmount the entire component for markers loading - just render without markers
    // This prevents Konva animations from being destroyed and recreated

    const getMarkerStyle = (marker: MarkerType) => {
        const latestEvent = getLatestEventByMarkerId(String(marker.id));

        // Enhanced alert detection - check for multiple alert conditions
        const isAlert =
            latestEvent &&
            ((latestEvent.severity?.toLowerCase() === 'critical' &&
                !['resolved', 'closed'].includes(latestEvent.state?.toLowerCase() ?? '')) ||
                (latestEvent.severity?.toLowerCase() === 'high' &&
                    ['active', 'triggered', 'alarm'].includes(latestEvent.state?.toLowerCase() ?? '')) ||
                (marker.type === 'fire' && latestEvent.state?.toLowerCase() === 'alarm_triggered'));

        if (isAlert) {
            return {
                circleFill: '#EC4747',
                fileName: marker.type,
                isAlert: true,
            };
        }

        // Enhanced color scheme with better contrast
        switch (marker.type) {
            case 'door':
                return { circleFill: '#10BCAD', fileName: 'door', isAlert: false };
            case 'camera':
                return { circleFill: '#877BD7', fileName: 'camera', isAlert: false };
            case 'fire':
                return { circleFill: '#E87027', fileName: 'fire', isAlert: false };
            case 'gate':
                return { circleFill: '#5C9DD5', fileName: 'gate', isAlert: false };
            case 'people':
                return { circleFill: '#EAB324', fileName: 'people', isAlert: false };
            case 'person':
                return { circleFill: '#EAB324', fileName: 'person', isAlert: false };
            case 'volumeOn':
                return { circleFill: '#7ED321', fileName: 'volumeOn', isAlert: false };
            default:
                return { circleFill: '#9B9B9B', fileName: 'building', isAlert: false };
        }
    };

    // ✅ fixed sizes per type
    const cardSizes: Record<string, { width: number; height: number }> = {
        camera: { width: 450, height: 320 },
        default: { width: 400, height: 250 },
    };

    return (
        <div
            ref={containerRef}
            className={`w-full h-screen overflow-auto relative bg-gray-900 ${className ?? ''}`}
            style={{ overflowX: 'auto', overflowY: 'auto' }}>
            {plan && dimensions.width > 0 && (
                <div
                    style={{
                        width: dimensions.width * zoom,
                        height: dimensions.height * zoom,
                    }}>
                    <Stage
                        width={dimensions.width * zoom}
                        height={dimensions.height * zoom}
                        scaleX={zoom}
                        scaleY={zoom}
                        onWheel={handleWheel}>
                        <Layer>
                            <KonvaImage image={plan} width={dimensions.width} height={dimensions.height} />
                            {/* Only render markers when not loading to prevent animation interruption */}
                            {!markersLoading &&
                                markers.map((m) => {
                                    const style = getMarkerStyle(m);
                                    const x =
                                        m.positionXPercent !== undefined
                                            ? (m.positionXPercent / 100) * dimensions.width
                                            : (m.positionX / plan.width) * dimensions.width;
                                    const y =
                                        m.positionYPercent !== undefined
                                            ? (m.positionYPercent / 100) * dimensions.height
                                            : (m.positionY / plan.height) * dimensions.height;

                                    return (
                                        <Marker
                                            key={m.id}
                                            x={x}
                                            y={y}
                                            fileName={style.fileName}
                                            radius={30}
                                            circleFill={style.circleFill}
                                            circleStroke={style.circleFill}
                                            pathStroke="black"
                                            isAlert={style.isAlert}
                                            onClick={() => setSelectedMarkerId(m.id.toString())}
                                        />
                                    );
                                })}
                        </Layer>
                    </Stage>

                    {/* Simple zoom buttons */}
                    <div className="fixed top-100 right-100 flex flex-col gap-2 z-50">
                        <button
                            className="w-10 h-10 flex items-center justify-center bg-gray-700/90 rounded-full shadow text-white"
                            onClick={() => {
                                const newZoom = Math.min(zoom + 0.1, 3);
                                setZoom(newZoom);
                                onZoomChange?.(newZoom);
                            }}>
                            <SvgIcon name="add" size="default" color="white" />
                        </button>

                        <button
                            className="w-10 h-10 flex items-center justify-center bg-gray-700/90 rounded-full shadow text-white"
                            onClick={() => {
                                const newZoom = Math.max(zoom - 0.1, 0.5);
                                setZoom(newZoom);
                                onZoomChange?.(newZoom);
                            }}>
                            <SvgIcon name="minus" size="default" color="white" />
                        </button>
                    </div>

                    {/* MarkerCard via portal */}
                    {selectedMarkerId &&
                        (() => {
                            const marker = markers.find((m) => m.id.toString() === selectedMarkerId);
                            if (!marker || !containerRef.current) return null;

                            const style = getMarkerStyle(marker);

                            // ✅ marker coordinates in canvas
                            const markerLeft = (((marker.positionXPercent ?? 0) + 5.5) / 100) * dimensions.width;
                            const markerTop = (((marker.positionYPercent ?? 0) + 4.5) / 100) * dimensions.height;

                            // ✅ account for zoom
                            const scaledLeft = markerLeft * zoom;
                            const scaledTop = markerTop * zoom;

                            // ✅ account for container scroll instead of rect
                            const scrollLeft = containerRef.current.scrollLeft;
                            const scrollTop = containerRef.current.scrollTop;

                            let left = scaledLeft - scrollLeft;
                            let top = scaledTop - scrollTop;

                            const { width: cardWidth, height: cardHeight } =
                                cardSizes[marker.type] ?? cardSizes.default;
                            const padding = 16;

                            // clamp within viewport
                            left = Math.min(
                                Math.max(left, padding + cardWidth / 2),
                                window.innerWidth - padding - cardWidth / 2,
                            );

                            const placeAbove = top - cardHeight - padding > 0;
                            if (placeAbove) {
                                top = Math.max(top, padding + cardHeight);
                            } else {
                                top = Math.min(top + cardHeight, window.innerHeight - padding);
                            }

                            return createPortal(
                                <div
                                    style={{
                                        position: 'absolute',
                                        left,
                                        top,
                                        transform: placeAbove ? 'translate(-50%, -100%)' : 'translate(-50%, 0)',
                                        zIndex: 9999,
                                        pointerEvents: 'auto',
                                    }}>
                                    <MarkerCard
                                        markerId={selectedMarkerId}
                                        outlineColor={style.circleFill}
                                        onClose={() => setSelectedMarkerId(null)}
                                    />
                                </div>,
                                document.body,
                            );
                        })()}
                </div>
            )}
        </div>
    );
}
