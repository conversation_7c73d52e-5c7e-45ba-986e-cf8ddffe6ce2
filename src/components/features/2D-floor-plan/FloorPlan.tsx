'use client';

import { Stage, Layer, Image as KonvaImage } from 'react-konva';
import useImage from 'use-image';
import { useEffect, useState, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import Konva from 'konva';
import { useMarkersStore } from '@/stores/markers.store';
import { Marker } from './Marker';
import { Marker as MarkerType } from '@/infrastructure/api/markers';
import MarkerCard from './MarkerCard';
import { SvgIcon } from '@/components/common';
import { useEventsStore } from '@/stores/alert.store';

export interface FloorPlanViewerProps {
    className?: string;
    floorPlanUrl?: string;
    floorPlanAlt?: string;
    selectedFloor?: string;
    selectedBuilding?: string;
    onPlanClick?: (coordinates: { x: number; y: number }) => void;
    zoom?: number;
    onZoomChange?: (zoom: number) => void;
    panPosition?: { x: number; y: number };
    onPanChange?: (position: { x: number; y: number }) => void;
    isLoading?: boolean;
    error?: string;
}

export default function FloorPlan({
    className,
    floorPlanUrl = '/2dFloorPlan.png',
    zoom: zoomProp = 1,
    onZoomChange,
    error,
}: FloorPlanViewerProps) {
    const [plan] = useImage(floorPlanUrl);
    const { markers, isLoading: markersLoading } = useMarkersStore();
    const containerRef = useRef<HTMLDivElement>(null);
    const stageRef = useRef<Konva.Stage>(null);
    const [dimensions, setDimensions] = useState({ width: 0, height: 0, scale: 1 });
    const [selectedMarkerId, setSelectedMarkerId] = useState<string | null>(null);
    const [zoom, setZoom] = useState(zoomProp);
    const [isInitialLoad, setIsInitialLoad] = useState(true);
    // Subscribe to events so FloorPlan re-renders when events update
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const events = useEventsStore((s) => s.events);

    const getLatestEventByMarkerId = useEventsStore((s) => s.getLatestEventByMarkerId);

    // keep in sync with external zoom prop
    useEffect(() => {
        setZoom(zoomProp);
    }, [zoomProp]);

    // Center the floor plan in the container
    const centerFloorPlan = useCallback(() => {
        if (!containerRef.current || !plan) return;

        const container = containerRef.current;
        const containerWidth = container.clientWidth;
        const containerHeight = container.clientHeight;

        // Calculate the zoomed content size
        const zoomedWidth = dimensions.width * zoom;
        const zoomedHeight = dimensions.height * zoom;

        // Center the content
        const scrollLeft = Math.max(0, (zoomedWidth - containerWidth) / 2);
        const scrollTop = Math.max(0, (zoomedHeight - containerHeight) / 2);

        container.scrollTo({
            left: scrollLeft,
            top: scrollTop,
            behavior: isInitialLoad ? 'auto' : 'smooth'
        });

        if (isInitialLoad) {
            setIsInitialLoad(false);
        }
    }, [dimensions.width, dimensions.height, zoom, plan, isInitialLoad]);

    // Calculate responsive dimensions
    useEffect(() => {
        const calculateDimensions = () => {
            if (!plan || !containerRef.current) return;
            const container = containerRef.current;
            const containerWidth = container.clientWidth;
            const containerHeight = container.clientHeight;

            // Calculate base scale to fit the image in the container
            const scaleX = containerWidth / plan.width;
            const scaleY = containerHeight / plan.height;
            const scale = Math.min(scaleX, scaleY * 0.9); // Use min to ensure it fits, with some padding

            setDimensions({
                width: plan.width * scale,
                height: plan.height * scale,
                scale,
            });
        };

        calculateDimensions();
        window.addEventListener('resize', calculateDimensions);
        return () => window.removeEventListener('resize', calculateDimensions);
    }, [plan]);

    // Center the floor plan when dimensions or zoom changes
    useEffect(() => {
        if (dimensions.width > 0 && dimensions.height > 0) {
            // Small delay to ensure DOM is updated
            const timeoutId = setTimeout(centerFloorPlan, 100);
            return () => clearTimeout(timeoutId);
        }
    }, [dimensions, zoom, centerFloorPlan]);

    useEffect(() => {
        const unsubscribe = useMarkersStore.getState().subscribeToFloorStore();
        return unsubscribe; // cleanup when unmounting
    }, []);

    // ✅ Typed Konva wheel event with zoom centering
    const handleWheel = useCallback(
        (e: Konva.KonvaEventObject<WheelEvent>) => {
            e.evt.preventDefault();

            if (!containerRef.current || !stageRef.current) return;

            const container = containerRef.current;
            const stage = stageRef.current;

            // Get mouse position relative to the stage
            const pointer = stage.getPointerPosition();
            if (!pointer) return;

            // Get current scroll position
            const scrollLeft = container.scrollLeft;
            const scrollTop = container.scrollTop;

            // Calculate mouse position in the scrollable content
            const mouseX = pointer.x / zoom + scrollLeft;
            const mouseY = pointer.y / zoom + scrollTop;

            const delta = e.evt.deltaY > 0 ? -0.1 : 0.1;
            const newZoom = Math.min(Math.max(zoom + delta, 0.5), 3); // clamp between 0.5–3

            setZoom(newZoom);
            onZoomChange?.(newZoom);

            // Calculate new scroll position to keep mouse position centered
            setTimeout(() => {
                const newScrollLeft = mouseX * newZoom - pointer.x;
                const newScrollTop = mouseY * newZoom - pointer.y;

                container.scrollTo({
                    left: Math.max(0, newScrollLeft),
                    top: Math.max(0, newScrollTop),
                    behavior: 'auto'
                });
            }, 0);
        },
        [zoom, onZoomChange],
    );

    // Only show loading for critical errors or when floor plan image fails to load
    if (error) {
        return <div className="text-red-600">Error: {error}</div>;
    }

    // Don't unmount the entire component for markers loading - just render without markers
    // This prevents Konva animations from being destroyed and recreated

    const getMarkerStyle = (marker: MarkerType) => {
        const latestEvent = getLatestEventByMarkerId(String(marker.id));

        // Enhanced alert detection - check for multiple alert conditions
        const isAlert =
            latestEvent &&
            ((latestEvent.severity?.toLowerCase() === 'critical' &&
                !['resolved', 'closed'].includes(latestEvent.state?.toLowerCase() ?? '')) ||
                (latestEvent.severity?.toLowerCase() === 'high' &&
                    ['active', 'triggered', 'alarm'].includes(latestEvent.state?.toLowerCase() ?? '')) ||
                (marker.type === 'fire' && latestEvent.state?.toLowerCase() === 'alarm_triggered'));

        if (isAlert) {
            return {
                circleFill: '#EC4747',
                fileName: marker.type,
                isAlert: true,
            };
        }

        // Enhanced color scheme with better contrast
        switch (marker.type) {
            case 'door':
                return { circleFill: '#10BCAD', fileName: 'door', isAlert: false };
            case 'camera':
                return { circleFill: '#877BD7', fileName: 'camera', isAlert: false };
            case 'fire':
                return { circleFill: '#E87027', fileName: 'fire', isAlert: false };
            case 'gate':
                return { circleFill: '#5C9DD5', fileName: 'gate', isAlert: false };
            case 'people':
                return { circleFill: '#EAB324', fileName: 'people', isAlert: false };
            case 'person':
                return { circleFill: '#EAB324', fileName: 'person', isAlert: false };
            case 'volumeOn':
                return { circleFill: '#7ED321', fileName: 'volumeOn', isAlert: false };
            default:
                return { circleFill: '#9B9B9B', fileName: 'building', isAlert: false };
        }
    };

    // ✅ fixed sizes per type
    const cardSizes: Record<string, { width: number; height: number }> = {
        camera: { width: 450, height: 320 },
        default: { width: 400, height: 250 },
    };

    return (
        <div
            ref={containerRef}
            className={`w-full h-screen overflow-auto relative bg-gray-900 ${className ?? ''}`}
            style={{
                overflowX: 'auto',
                overflowY: 'auto',
                scrollBehavior: 'smooth'
            }}>
            {plan && dimensions.width > 0 && (
                <div
                    style={{
                        width: dimensions.width * zoom,
                        height: dimensions.height * zoom,
                        minWidth: '100%',
                        minHeight: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                    }}>
                    <Stage
                        ref={stageRef}
                        width={dimensions.width * zoom}
                        height={dimensions.height * zoom}
                        scaleX={1}
                        scaleY={1}
                        onWheel={handleWheel}>
                        <Layer>
                            <KonvaImage
                                image={plan}
                                width={dimensions.width * zoom}
                                height={dimensions.height * zoom}
                            />
                            {/* Only render markers when not loading to prevent animation interruption */}
                            {!markersLoading &&
                                markers.map((m) => {
                                    const style = getMarkerStyle(m);
                                    const x =
                                        m.positionXPercent !== undefined
                                            ? (m.positionXPercent / 100) * dimensions.width * zoom
                                            : (m.positionX / plan.width) * dimensions.width * zoom;
                                    const y =
                                        m.positionYPercent !== undefined
                                            ? (m.positionYPercent / 100) * dimensions.height * zoom
                                            : (m.positionY / plan.height) * dimensions.height * zoom;

                                    return (
                                        <Marker
                                            key={m.id}
                                            x={x}
                                            y={y}
                                            fileName={style.fileName}
                                            radius={30 * zoom}
                                            circleFill={style.circleFill}
                                            circleStroke={style.circleFill}
                                            pathStroke="black"
                                            isAlert={style.isAlert}
                                            onClick={() => setSelectedMarkerId(m.id.toString())}
                                        />
                                    );
                                })}
                        </Layer>
                    </Stage>

                    {/* Enhanced zoom controls */}
                    <div className="fixed top-4 right-4 flex flex-col gap-2 z-50">
                        <button
                            className="w-12 h-12 flex items-center justify-center bg-gray-700/90 hover:bg-gray-600/90 rounded-full shadow-lg text-white transition-colors"
                            onClick={() => {
                                const newZoom = Math.min(zoom + 0.2, 3);
                                setZoom(newZoom);
                                onZoomChange?.(newZoom);
                            }}
                            title="Zoom In">
                            <SvgIcon name="add" size="default" color="white" />
                        </button>

                        <button
                            className="w-12 h-12 flex items-center justify-center bg-gray-700/90 hover:bg-gray-600/90 rounded-full shadow-lg text-white transition-colors"
                            onClick={() => {
                                const newZoom = Math.max(zoom - 0.2, 0.5);
                                setZoom(newZoom);
                                onZoomChange?.(newZoom);
                            }}
                            title="Zoom Out">
                            <SvgIcon name="minus" size="default" color="white" />
                        </button>

                        <button
                            className="w-12 h-12 flex items-center justify-center bg-gray-700/90 hover:bg-gray-600/90 rounded-full shadow-lg text-white transition-colors"
                            onClick={() => {
                                setZoom(1);
                                onZoomChange?.(1);
                                setTimeout(centerFloorPlan, 100);
                            }}
                            title="Reset Zoom & Center">
                            <SvgIcon name="home" size="default" color="white" />
                        </button>
                    </div>

                    {/* MarkerCard via portal */}
                    {selectedMarkerId &&
                        (() => {
                            const marker = markers.find((m) => m.id.toString() === selectedMarkerId);
                            if (!marker || !containerRef.current) return null;

                            const style = getMarkerStyle(marker);

                            // ✅ marker coordinates in canvas
                            const markerLeft = (((marker.positionXPercent ?? 0) + 5.5) / 100) * dimensions.width;
                            const markerTop = (((marker.positionYPercent ?? 0) + 4.5) / 100) * dimensions.height;

                            // ✅ account for zoom
                            const scaledLeft = markerLeft * zoom;
                            const scaledTop = markerTop * zoom;

                            // ✅ account for container scroll instead of rect
                            const scrollLeft = containerRef.current.scrollLeft;
                            const scrollTop = containerRef.current.scrollTop;

                            let left = scaledLeft - scrollLeft;
                            let top = scaledTop - scrollTop;

                            const { width: cardWidth, height: cardHeight } =
                                cardSizes[marker.type] ?? cardSizes.default;
                            const padding = 16;

                            // clamp within viewport
                            left = Math.min(
                                Math.max(left, padding + cardWidth / 2),
                                window.innerWidth - padding - cardWidth / 2,
                            );

                            const placeAbove = top - cardHeight - padding > 0;
                            if (placeAbove) {
                                top = Math.max(top, padding + cardHeight);
                            } else {
                                top = Math.min(top + cardHeight, window.innerHeight - padding);
                            }

                            return createPortal(
                                <div
                                    style={{
                                        position: 'absolute',
                                        left,
                                        top,
                                        transform: placeAbove ? 'translate(-50%, -100%)' : 'translate(-50%, 0)',
                                        zIndex: 9999,
                                        pointerEvents: 'auto',
                                    }}>
                                    <MarkerCard
                                        markerId={selectedMarkerId}
                                        outlineColor={style.circleFill}
                                        onClose={() => setSelectedMarkerId(null)}
                                    />
                                </div>,
                                document.body,
                            );
                        })()}
                </div>
            )}
        </div>
    );
}
