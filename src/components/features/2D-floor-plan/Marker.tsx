'use client';

import { useSvgCleanPaths } from '@/hooks/useSvgPaths';
import { Group, Circle, Path, Ring } from 'react-konva';
import { useEffect, useRef, useState } from 'react';
import Konva from 'konva';

interface SvgIconProps {
    x: number;
    y: number;
    fileName: string;
    radius?: number;
    circleFill?: string;
    circleStroke?: string;
    pathFill?: string;
    pathStroke?: string;
    isAlert?: boolean;
    onClick?: () => void;
}

export function Marker({
    x,
    y,
    fileName,
    radius = 30,
    circleFill = 'lightblue',
    circleStroke = 'black',
    pathFill = '',
    pathStroke = 'black',
    isAlert = false,
    onClick,
}: SvgIconProps) {
    const paths = useSvgCleanPaths(fileName);
    const groupRef = useRef<Konva.Group>(null);
    const circleRef = useRef<Konva.Circle>(null);
    const ringRef = useRef<Konva.Ring>(null);
    const pathGroupRef = useRef<Konva.Group>(null);
    const [animationPhase, setAnimationPhase] = useState(0);

    // ✅ Enhanced multi-layer alert animations
    useEffect(() => {
        if (!isAlert || !groupRef.current || !circleRef.current || !ringRef.current || !pathGroupRef.current) return;

        const group = groupRef.current;
        const circle = circleRef.current;
        const ring = ringRef.current;
        const pathGroup = pathGroupRef.current;

        // 1. Main pulsing animation for the entire marker
        const mainPulse = new Konva.Tween({
            node: group,
            duration: 1.5,
            scaleX: 1.2,
            scaleY: 1.2,
            yoyo: true,
            repeat: -1,
            easing: Konva.Easings.EaseInOut,
        });

        // 2. Circle color pulsing animation
        const colorPulse = new Konva.Tween({
            node: circle,
            duration: 1,
            fill: '#FF6B6B', // Bright red for alert
            opacity: 0.8,
            yoyo: true,
            repeat: -1,
            easing: Konva.Easings.EaseInOut,
        });

        // 3. Expanding ring animation (ripple effect)
        const ringExpand = new Konva.Tween({
            node: ring,
            duration: 2,
            outerRadius: radius * 2,
            opacity: 0,
            repeat: -1,
            easing: Konva.Easings.EaseOut,
            onFinish: () => {
                ring.outerRadius(radius * 1.2);
                ring.opacity(0.6);
            }
        });

        // 4. Icon bouncing animation
        const iconBounce = new Konva.Tween({
            node: pathGroup,
            duration: 0.8,
            scaleX: 1.3,
            scaleY: 1.3,
            yoyo: true,
            repeat: -1,
            easing: Konva.Easings.BackEaseOut,
        });

        // Start all animations
        mainPulse.play();
        colorPulse.play();
        ringExpand.play();
        iconBounce.play();

        // Phase cycling for varied effects
        const phaseInterval = setInterval(() => {
            setAnimationPhase(prev => (prev + 1) % 3);
        }, 2000);

        // Cleanup animations
        return () => {
            mainPulse.destroy();
            colorPulse.destroy();
            ringExpand.destroy();
            iconBounce.destroy();
            clearInterval(phaseInterval);
        };
    }, [isAlert, radius]);

    // ✅ special case for "people"
    const isPeople = fileName === 'people' || fileName === 'person';
    const pathScaleX = isPeople ? 0.6 : 1;
    const pathScaleY = isPeople ? 0.6 : 1;
    const pathOffsetX = isPeople ? 18 : 12;
    const pathOffsetY = isPeople ? 12 : 12;

    return (
        <Group x={x} y={y} onClick={onClick} ref={groupRef}>
            {/* Alert ripple ring - only visible during alerts */}
            {isAlert && (
                <Ring
                    ref={ringRef}
                    innerRadius={radius * 1.1}
                    outerRadius={radius * 1.2}
                    fill="#FF4444"
                    opacity={0.6}
                    name="alert-ring"
                />
            )}

            {/* Main circle with enhanced alert styling */}
            <Circle
                ref={circleRef}
                radius={radius}
                fill={isAlert ? '#EC4747' : circleFill}
                stroke={isAlert ? '#FF6B6B' : circleStroke}
                strokeWidth={isAlert ? 3 : 1}
                opacity={isAlert ? 0.9 : 0.9}
                name="device-marker"
                scaleX={0.7}
                scaleY={0.7}
                shadowColor={isAlert ? '#FF4444' : 'transparent'}
                shadowBlur={isAlert ? 10 : 0}
                shadowOpacity={isAlert ? 0.8 : 0}
            />

            {/* Alert glow effect - outer ring */}
            {isAlert && (
                <Circle
                    radius={radius * 1.4}
                    fill="transparent"
                    stroke="#FF6B6B"
                    strokeWidth={2}
                    opacity={0.4}
                    name="alert-glow"
                    scaleX={0.7}
                    scaleY={0.7}
                />
            )}

            {/* Icon paths with enhanced alert effects */}
            <Group ref={pathGroupRef}>
                {paths.map((p, i) => (
                    <Path
                        key={i}
                        data={p.d}
                        fill={isAlert ? '#FFFFFF' : pathFill}
                        stroke={isAlert ? '#FFFFFF' : pathStroke}
                        strokeWidth={isAlert ? 2 : 1}
                        scaleX={pathScaleX}
                        scaleY={pathScaleY}
                        offsetX={pathOffsetX}
                        offsetY={pathOffsetY}
                        shadowColor={isAlert ? '#000000' : 'transparent'}
                        shadowBlur={isAlert ? 2 : 0}
                        shadowOpacity={isAlert ? 0.5 : 0}
                    />
                ))}
            </Group>

            {/* Alert indicator badge */}
            {isAlert && (
                <Group x={radius * 0.6} y={-radius * 0.6}>
                    <Circle
                        radius={8}
                        fill="#FF0000"
                        stroke="#FFFFFF"
                        strokeWidth={2}
                        name="alert-badge"
                    />
                    <Circle
                        radius={3}
                        fill="#FFFFFF"
                        name="alert-badge-dot"
                    />
                </Group>
            )}
        </Group>
    );
}
