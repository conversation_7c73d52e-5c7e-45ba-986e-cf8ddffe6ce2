'use client';

import { useSvgCleanPaths } from '@/hooks/useSvgPaths';
import { Group, Circle, Path } from 'react-konva';
import { useEffect, useRef } from 'react';
import Konva from 'konva';

interface SvgIconProps {
    x: number;
    y: number;
    fileName: string;
    radius?: number;
    circleFill?: string;
    circleStroke?: string;
    pathFill?: string;
    pathStroke?: string;
    isAlert?: boolean;
    onClick?: () => void;
}

export function Marker({
    x,
    y,
    fileName,
    radius = 30,
    circleFill = 'lightblue',
    circleStroke = 'black',
    pathFill = '',
    pathStroke = 'black',
    isAlert = false,
    onClick,
}: SvgIconProps) {
    const paths = useSvgCleanPaths(fileName);
    const groupRef = useRef<Konva.Group>(null);
    const wave1Ref = useRef<Konva.Circle>(null);
    const wave2Ref = useRef<Konva.Circle>(null);
    const wave3Ref = useRef<Konva.Circle>(null);

    // ✅ Simple alert animations with outline waves
    useEffect(() => {
        if (!isAlert || !groupRef.current || !wave1Ref.current || !wave2Ref.current || !wave3Ref.current) return;

        const group = groupRef.current;
        const wave1 = wave1Ref.current;
        const wave2 = wave2Ref.current;
        const wave3 = wave3Ref.current;

        // Main pulsing animation for the entire marker
        const pulseAnimation = new Konva.Tween({
            node: group,
            duration: 2,
            scaleX: 1.1,
            scaleY: 1.1,
            opacity: 0.8,
            yoyo: true,
            repeat: -1,
            easing: Konva.Easings.EaseInOut,
        });

        // Wave 1 - Inner wave
        const wave1Animation = new Konva.Tween({
            node: wave1,
            duration: 2,
            radius: radius * 1.8,
            opacity: 0,
            repeat: -1,
            easing: Konva.Easings.EaseOut,
            onFinish: () => {
                wave1.radius(radius * 1.2);
                wave1.opacity(0.6);
            }
        });

        // Wave 2 - Middle wave (delayed)
        const wave2Animation = new Konva.Tween({
            node: wave2,
            duration: 2,
            radius: radius * 1.8,
            opacity: 0,
            repeat: -1,
            easing: Konva.Easings.EaseOut,
            onFinish: () => {
                wave2.radius(radius * 1.2);
                wave2.opacity(0.4);
            }
        });

        // Wave 3 - Outer wave (more delayed)
        const wave3Animation = new Konva.Tween({
            node: wave3,
            duration: 2,
            radius: radius * 1.8,
            opacity: 0,
            repeat: -1,
            easing: Konva.Easings.EaseOut,
            onFinish: () => {
                wave3.radius(radius * 1.2);
                wave3.opacity(0.2);
            }
        });

        // Start animations with delays for wave effect
        pulseAnimation.play();
        wave1Animation.play();

        setTimeout(() => wave2Animation.play(), 400);
        setTimeout(() => wave3Animation.play(), 800);

        // Cleanup animations
        return () => {
            pulseAnimation.destroy();
            wave1Animation.destroy();
            wave2Animation.destroy();
            wave3Animation.destroy();
        };
    }, [isAlert, radius]);

    // ✅ special case for "people"
    const isPeople = fileName === 'people' || fileName === 'person';
    const pathScaleX = isPeople ? 0.6 : 1;
    const pathScaleY = isPeople ? 0.6 : 1;
    const pathOffsetX = isPeople ? 18 : 12;
    const pathOffsetY = isPeople ? 12 : 12;

    return (
        <Group x={x} y={y} onClick={onClick} ref={groupRef}>
            {/* Alert wave circles - only visible during alerts */}
            {isAlert && (
                <>
                    <Circle
                        ref={wave1Ref}
                        radius={radius * 1.2}
                        fill="transparent"
                        stroke="#FF6B6B"
                        strokeWidth={2}
                        opacity={0.6}
                        name="wave-1"
                    />
                    <Circle
                        ref={wave2Ref}
                        radius={radius * 1.2}
                        fill="transparent"
                        stroke="#FF6B6B"
                        strokeWidth={1.5}
                        opacity={0.4}
                        name="wave-2"
                    />
                    <Circle
                        ref={wave3Ref}
                        radius={radius * 1.2}
                        fill="transparent"
                        stroke="#FF6B6B"
                        strokeWidth={1}
                        opacity={0.2}
                        name="wave-3"
                    />
                </>
            )}

            {/* Main circle with simple alert styling */}
            <Circle
                radius={radius}
                fill={isAlert ? '#EC4747' : circleFill}
                stroke={isAlert ? '#FF6B6B' : circleStroke}
                strokeWidth={isAlert ? 2 : 1}
                opacity={0.9}
                name="device-marker"
                scaleX={0.7}
                scaleY={0.7}
            />

            {/* Icon paths with simple alert styling */}
            {paths.map((p, i) => (
                <Path
                    key={i}
                    data={p.d}
                    fill={isAlert ? '#FFFFFF' : pathFill}
                    stroke={isAlert ? '#FFFFFF' : pathStroke}
                    strokeWidth={isAlert ? 1.5 : 1}
                    scaleX={pathScaleX}
                    scaleY={pathScaleY}
                    offsetX={pathOffsetX}
                    offsetY={pathOffsetY}
                />
            ))}
        </Group>
    );
}
