'use client';

import { useSvgCleanPaths } from '@/hooks/useSvgPaths';
import { Group, Circle, Path } from 'react-konva';
import { useEffect, useRef } from 'react';
import Konva from 'konva';

interface SvgIconProps {
    x: number;
    y: number;
    fileName: string;
    radius?: number;
    circleFill?: string;
    circleStroke?: string;
    pathFill?: string;
    pathStroke?: string;
    isAlert?: boolean;
    onClick?: () => void;
}

export function Marker({
    x,
    y,
    fileName,
    radius = 30,
    circleFill = 'lightblue',
    circleStroke = 'black',
    pathFill = '',
    pathStroke = 'black',
    isAlert = false,
    onClick,
}: SvgIconProps) {
    const paths = useSvgCleanPaths(fileName);
    const groupRef = useRef<Konva.Group>(null);

    // ✅ Simple alert animations like floor plan editor
    useEffect(() => {
        if (!isAlert || !groupRef.current) return;

        const group = groupRef.current;

        // Simple pulsing animation for the entire marker (like floor plan editor)
        const pulseAnimation = new Konva.Tween({
            node: group,
            duration: 2,
            scaleX: 1.1,
            scaleY: 1.1,
            opacity: 0.8,
            yoyo: true,
            repeat: -1,
            easing: Konva.Easings.EaseInOut,
        });

        pulseAnimation.play();

        // Cleanup animation
        return () => {
            pulseAnimation.destroy();
        };
    }, [isAlert]);

    // ✅ special case for "people"
    const isPeople = fileName === 'people' || fileName === 'person';
    const pathScaleX = isPeople ? 0.6 : 1;
    const pathScaleY = isPeople ? 0.6 : 1;
    const pathOffsetX = isPeople ? 18 : 12;
    const pathOffsetY = isPeople ? 12 : 12;

    return (
        <Group x={x} y={y} onClick={onClick} ref={groupRef}>
            {/* Main circle with simple alert styling */}
            <Circle
                radius={radius}
                fill={isAlert ? '#EC4747' : circleFill}
                stroke={isAlert ? '#FF6B6B' : circleStroke}
                strokeWidth={isAlert ? 2 : 1}
                opacity={0.9}
                name="device-marker"
                scaleX={0.7}
                scaleY={0.7}
            />

            {/* Icon paths with simple alert styling */}
            {paths.map((p, i) => (
                <Path
                    key={i}
                    data={p.d}
                    fill={isAlert ? '#FFFFFF' : pathFill}
                    stroke={isAlert ? '#FFFFFF' : pathStroke}
                    strokeWidth={isAlert ? 1.5 : 1}
                    scaleX={pathScaleX}
                    scaleY={pathScaleY}
                    offsetX={pathOffsetX}
                    offsetY={pathOffsetY}
                />
            ))}
        </Group>
    );
}
