'use client';

import { useSvgCleanPaths } from '@/hooks/useSvgPaths';
import { Group, Circle, Path } from 'react-konva';
import { useEffect, useRef } from 'react';
import Konva from 'konva';

interface SvgIconProps {
    x: number;
    y: number;
    fileName: string;
    radius?: number;
    circleFill?: string;
    circleStroke?: string;
    pathFill?: string;
    pathStroke?: string;
    isAlert?: boolean;
    onClick?: () => void;
}

export function Marker({
    x,
    y,
    fileName,
    radius = 30,
    circleFill = 'lightblue',
    circleStroke = 'black',
    pathFill = '',
    pathStroke = 'black',
    isAlert = false,
    onClick,
}: SvgIconProps) {
    const paths = useSvgCleanPaths(fileName);
    const groupRef = useRef<Konva.Group>(null);
    const wave1Ref = useRef<Konva.Circle>(null);
    const wave2Ref = useRef<Konva.Circle>(null);
    const wave3Ref = useRef<Konva.Circle>(null);

    // ✅ Enhanced radar-style alert animations with continuous wave ripples
    useEffect(() => {
        if (!isAlert || !groupRef.current || !wave1Ref.current || !wave2Ref.current || !wave3Ref.current) return;

        const group = groupRef.current;
        const wave1 = wave1Ref.current;
        const wave2 = wave2Ref.current;
        const wave3 = wave3Ref.current;

        // Reset initial wave states
        wave1.radius(radius * 1.1);
        wave1.opacity(0.8);
        wave2.radius(radius * 1.1);
        wave2.opacity(0.6);
        wave3.radius(radius * 1.1);
        wave3.opacity(0.4);

        // Main pulsing animation for the entire marker
        const pulseAnimation = new Konva.Tween({
            node: group,
            duration: 1.5,
            scaleX: 1.1,
            scaleY: 1.1,
            opacity: 0.9,
            yoyo: true,
            repeat: -1,
            easing: Konva.Easings.EaseInOut,
        });

        // Store active tweens for cleanup
        const activeTweens: Konva.Tween[] = [];

        // Create continuous radar wave animations with smoother transitions
        const createWaveAnimation = (waveNode: Konva.Circle, initialOpacity: number, delay: number = 0) => {
            const animate = () => {
                // Reset wave to starting position (closer to marker edge for smoother transition)
                waveNode.radius(radius * 0.9);
                waveNode.opacity(initialOpacity);

                // Create expanding animation with smoother easing
                const tween = new Konva.Tween({
                    node: waveNode,
                    duration: 3, // Increased duration for smoother animation
                    radius: radius * 1.5,
                    opacity: 0,
                    easing: Konva.Easings.EaseInOut, // Changed to EaseInOut for smoother waves
                    onFinish: () => {
                        // Restart the animation with no gap for continuous effect
                        setTimeout(animate, 50); // Reduced gap for smoother continuous waves
                    },
                });
                activeTweens.push(tween);
                tween.play();
                return tween;
            };

            // Start with delay for staggered effect
            const timeoutId = setTimeout(animate, delay);
            return { animate, timeoutId };
        };

        // Start main pulse
        pulseAnimation.play();

        // Create staggered wave animations with closer timing for smoother radar effect
        const wave1Animation = createWaveAnimation(wave1, 0.7, 0);
        const wave2Animation = createWaveAnimation(wave2, 0.5, 400); // Reduced delay
        const wave3Animation = createWaveAnimation(wave3, 0.3, 800); // Reduced delay

        // Cleanup animations
        return () => {
            pulseAnimation.destroy();
            clearTimeout(wave1Animation.timeoutId);
            clearTimeout(wave2Animation.timeoutId);
            clearTimeout(wave3Animation.timeoutId);
            // Stop any ongoing wave animations
            activeTweens.forEach((tween) => tween.destroy());
        };
    }, [isAlert, radius]);

    // ✅ special case for "people"
    const isPeople = fileName === 'people' || fileName === 'person';
    const pathScaleX = isPeople ? 0.6 : 1;
    const pathScaleY = isPeople ? 0.6 : 1;
    const pathOffsetX = isPeople ? 18 : 12;
    const pathOffsetY = isPeople ? 12 : 12;

    return (
        <Group x={x} y={y} onClick={onClick} ref={groupRef}>
            {/* Alert expanding wave ripples - only visible during alerts */}
            {isAlert && (
                <>
                    <Circle
                        ref={wave1Ref}
                        radius={radius * 0.9}
                        fill="transparent"
                        stroke="#FF6B6B"
                        strokeWidth={2}
                        opacity={0.7}
                        name="wave-ripple-1"
                    />
                    <Circle
                        ref={wave2Ref}
                        radius={radius * 0.9}
                        fill="transparent"
                        stroke="#FF6B6B"
                        strokeWidth={1.5}
                        opacity={0.5}
                        name="wave-ripple-2"
                    />
                    <Circle
                        ref={wave3Ref}
                        radius={radius * 0.9}
                        fill="transparent"
                        stroke="#FF6B6B"
                        strokeWidth={1}
                        opacity={0.3}
                        name="wave-ripple-3"
                    />
                </>
            )}

            {/* Main circle with simple alert styling */}
            <Circle
                radius={radius}
                fill={isAlert ? '#EC4747' : circleFill}
                stroke={isAlert ? '#FF6B6B' : circleStroke}
                strokeWidth={isAlert ? 2 : 1}
                opacity={0.9}
                name="device-marker"
                scaleX={0.6} // Reduced from 0.7 to 0.6
                scaleY={0.6} // Reduced from 0.7 to 0.6
            />

            {/* Icon paths with simple alert styling */}
            {paths.map((p, i) => (
                <Path
                    key={i}
                    data={p.d}
                    fill={isAlert ? '#FFFFFF' : pathFill}
                    stroke={isAlert ? '#FFFFFF' : pathStroke}
                    strokeWidth={isAlert ? 1.5 : 1}
                    scaleX={pathScaleX}
                    scaleY={pathScaleY}
                    offsetX={pathOffsetX}
                    offsetY={pathOffsetY}
                />
            ))}
        </Group>
    );
}
