import { SystemEntity } from './system.types';
import { SystemMetrics } from '@/stores/markers.store';

/**
 * Merges API system data with marker-based metrics
 * Uses API data for title, iconName, iconColor and other static fields
 * Uses marker data only for total device/count metrics
 */
export function mergeSystemsWithMarkerMetrics(
    apiSystems: SystemEntity[],
    markerMetrics: SystemMetrics,
): SystemEntity[] {
    return apiSystems.map((system) => {
        const updatedMetrics = system.metrics.map((metric) => {
            // Update only the total count metrics based on marker data using metric key
            switch (metric.key) {
                case 'fire_total_devices':
                    return { ...metric, value: markerMetrics.fireDevices.toString() };
                case 'access_total_doors':
                    return { ...metric, value: markerMetrics.totalDoors.toString() };
                case 'cctv_total_cameras':
                    return { ...metric, value: markerMetrics.totalCameras.toString() };
                case 'gate_total_barriers':
                    return { ...metric, value: markerMetrics.totalBarriers.toString() };
                case 'pa_total_devices':
                    return { ...metric, value: markerMetrics.publicAddressDevices.toString() };
                case 'presence_total_devices':
                    return { ...metric, value: markerMetrics.presenceDevices.toString() };
                default:
                    return metric; // Keep original metric if no match
            }
        });

        return { ...system, metrics: updatedMetrics };
    });
}
