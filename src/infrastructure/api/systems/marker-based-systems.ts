import { Systems, System } from './types';
import { SystemMetrics } from '@/stores/markers.store';

/**
 * Merges API system data with marker-based metrics
 * Uses API data for title, iconName, iconColor and other static fields
 * Uses marker data only for total device/count metrics
 */
export function mergeSystemsWithMarkerMetrics(apiSystems: System[], markerMetrics: SystemMetrics): System[] {
    return apiSystems.map((system) => {
        const updatedMetrics = system.metrics.map((metric) => {
            // Update only the total count metrics based on marker data using system code and metric key
            switch (system.code) {
                case 'fire':
                    if (metric.key === 'fire_total_devices') {
                        return { ...metric, value: markerMetrics.fireDevices.toString() };
                    }
                    break;
                case 'access':
                    if (metric.key === 'access_total_doors') {
                        return { ...metric, value: markerMetrics.totalDoors.toString() };
                    }
                    break;
                case 'cctv':
                    if (metric.key === 'cctv_total_cameras') {
                        return { ...metric, value: markerMetrics.totalCameras.toString() };
                    }
                    break;
                case 'gate':
                    if (metric.key === 'gate_total_barriers') {
                        return { ...metric, value: markerMetrics.totalBarriers.toString() };
                    }
                    break;
                case 'pa':
                    if (metric.key === 'pa_total_devices') {
                        return { ...metric, value: markerMetrics.publicAddressDevices.toString() };
                    }
                    break;
                case 'presence':
                    if (metric.key === 'presence_total_devices') {
                        return { ...metric, value: markerMetrics.presenceDevices.toString() };
                    }
                    break;
            }
            return metric; // Keep original metric if no match
        });

        return { ...system, metrics: updatedMetrics };
    });
}