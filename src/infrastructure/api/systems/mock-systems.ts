import { Systems } from './types';

export const mockSystems: Systems = {
    systems: [
        {
            title: 'Fire Alarm System',
            code: 'fire',
            iconName: 'fire',
            iconColor: '#E87027',
            metrics: [
                { key: 'fire_total_devices', label: 'Total devices', value: '200', isAlert: false, textAlign: 'start' },
                { key: 'fire_active_alarms', label: 'Active Alarms', value: '2', isAlert: true, textAlign: 'end' },
            ],
        },
        {
            title: 'Access Control',
            code: 'access',
            iconName: 'door',
            iconColor: '#10BCAD',
            metrics: [
                { key: 'access_total_doors', label: 'Total doors', value: '150', isAlert: false, textAlign: 'start' },
                { key: 'access_open_closed', label: 'Open/Closed', value: '6/144', isAlert: false, textAlign: 'end' },
            ],
        },
        {
            title: 'CCTV Surveillance',
            code: 'cctv',
            iconName: 'camera',
            iconColor: '#877BD7',
            metrics: [
                { key: 'cctv_total_cameras', label: 'Total cameras', value: '200', isAlert: false, textAlign: 'start' },
                { key: 'cctv_active_incidents', label: 'Active Incidents', value: '2', isAlert: true, textAlign: 'end' },
            ],
        },
        {
            title: 'Gate Barrier',
            code: 'gate',
            iconName: 'gateBarrier',
            iconColor: '#5C9DD5',
            metrics: [
                { key: 'gate_total_barriers', label: 'Total barriers', value: '200', isAlert: false, textAlign: 'start' },
                { key: 'gate_unauthorized_attempts', label: 'Unauthorized attempts', value: '5', isAlert: true, textAlign: 'end' },
            ],
        },
        {
            title: 'Public Address System',
            code: 'pa',
            iconName: 'volume-up',
            iconColor: '#6f42c1',
            metrics: [
                { key: 'pa_total_devices', label: 'Total devices', value: '0', isAlert: false, textAlign: 'start' },
                { key: 'pa_active_critical', label: 'Active Critical', value: '1', isAlert: true, textAlign: 'end' },
            ],
        },
        {
            title: 'Presence Detection System',
            code: 'presence',
            iconName: 'user-check',
            iconColor: '#17a2b8',
            metrics: [
                { key: 'presence_total_devices', label: 'Total devices', value: '0', isAlert: false, textAlign: 'start' },
                { key: 'presence_active_critical', label: 'Active Critical', value: '2', isAlert: true, textAlign: 'end' },
            ],
        },
    ],
};
