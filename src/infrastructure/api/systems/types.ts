// ---------------------- metric ----------------------
export interface Metric {
    key: string;
    label: string;
    value: string;
    isAlert: boolean;
    textAlign?: 'start' | 'center' | 'end';
}

// ---------------------- system ----------------------
export interface System {
    title: string;
    code: string;
    iconName: string;
    iconColor: string;
    metrics: Metric[];
}

// ---------------------- root ------------------------
export interface Systems {
    systems: System[];
}
