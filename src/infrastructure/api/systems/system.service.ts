import { nebularApi } from '@/infrastructure/api/base/nebular-api.config';
import { logger } from '@/infrastructure/logging';
import { appConfig } from '@/shared/config/app.config';
import type { GetSystemsApiResponse, SystemEntity } from './system.types';
import { mockSystems } from './mock-systems';

export class SystemService {
    private static instance: SystemService;
    private baseUrl: string;
    private systemPath = 'api/v1/systems';

    private constructor() {
        const config = appConfig.getConfig();
        this.baseUrl = config.nebularApi.baseUrl;
    }

    public static getInstance(): SystemService {
        if (!SystemService.instance) {
            SystemService.instance = new SystemService();
        }
        return SystemService.instance;
    }

    /**
     * Decide between real or mock systems
     */
    public async getSystems(): Promise<GetSystemsApiResponse> {
        logger.info('[SystemService] Fetching systems');

        try {
            const config = appConfig.getConfig();

            if (config.nebularApi.enableMockData) {
                return this.getMockSystems();
            }

            return this.getRealSystems();
        } catch (error: unknown) {
            logger.error('[SystemService] Error fetching systems:', error as Error);
            throw error;
        }
    }

    /**
     * Fetch real systems from API
     */
    private async getRealSystems(): Promise<GetSystemsApiResponse> {
        try {
            const response = await nebularApi.get<GetSystemsApiResponse>(`${this.baseUrl}/${this.systemPath}`);

            // ✅ Basic response validation
            if (!response.data || !Array.isArray(response.data.systems)) {
                logger.error('[SystemService] Invalid response format from systems API');
                throw new Error('Invalid API response format');
            }

            logger.info(`[SystemService] Loaded ${response.data.systems.length} systems from API`);
            return response.data;
        } catch (error) {
            logger.error('[SystemService] getRealSystems failed', error as Error);
            throw error;
        }
    }

    /**
     * Fetch mock systems
     */
    private async getMockSystems(): Promise<GetSystemsApiResponse> {
        logger.warn('[SystemService] Using mock systems data');

        const systems: SystemEntity[] = mockSystems.systems.map((system) => ({
            title: system.title,
            iconName: system.iconName,
            iconColor: system.iconColor,
            metrics: system.metrics.map((metric) => ({
                key: metric.key,
                label: metric.label,
                value: metric.value,
                isAlert: metric.isAlert,
                textAlign: metric.textAlign === 'end' ? 'end' : 'start',
            })),
        }));

        return Promise.resolve({ systems });
    }
}

// ✅ Export singleton instance
export const systemService = SystemService.getInstance();
