import { Building, BuildingStats, getBuildingByShortCode, getBuildingById, mockBuildings } from './buildings';
import { Zone, getZonesByBuildingId, getZoneByCode, getZoneById, mockZones } from './zones';
import { Floor, getFloorsByZoneId, getFloorByCode, getFloorById, mockFloors } from './floors';
import { Room, getRoomsByFloorId, getRoomByCode, getRoomById, mockRooms } from './rooms';
import { Door, DoorStats, getDoorsByRoomId, getDoorByCode, getDoorById, mockDoors } from './doors';

// Comprehensive geographic hierarchy interface
export interface GeographicHierarchy {
    buildings: Building[];
    zones: Zone[];
    floors: Floor[];
    rooms: Room[];
    doors: Door[];
}

// Navigation and location reference interfaces
export interface LocationReference {
    buildingName: string;
    buildingShortCode: string;
    zoneName: string;
    zoneCode: string;
    floorName: string;
    floorCode: string;
    roomName?: string;
    roomCode?: string;
    fullPath: string;
}

export interface NavigationContext {
    currentBuilding?: Building;
    currentZone?: Zone;
    currentFloor?: Floor;
    currentRoom?: Room;
    breadcrumb: string[];
}

// Geography Service Class
export class GeographyService {
    private static instance: GeographyService;
    private hierarchy: GeographicHierarchy;

    private constructor() {
        this.hierarchy = {
            buildings: mockBuildings,
            zones: mockZones,
            floors: mockFloors,
            rooms: mockRooms,
            doors: mockDoors,
        };
    }

    public static getInstance(): GeographyService {
        if (!GeographyService.instance) {
            GeographyService.instance = new GeographyService();
        }
        return GeographyService.instance;
    }

    // Building methods
    public getAllBuildings(): Building[] {
        return this.hierarchy.buildings.filter((b) => b.isActive);
    }

    public getBuildingByShortCode(shortCode: string): Building | undefined {
        return getBuildingByShortCode(shortCode);
    }

    public getBuildingById(id: number): Building | undefined {
        return getBuildingById(id);
    }

    public getBuildingStats(buildingId: number): BuildingStats | null {
        const building = this.getBuildingById(buildingId);
        if (!building) return null;

        const zones = this.getZonesByBuildingId(buildingId);
        const floors = zones.flatMap((zone) => this.getFloorsByZoneId(zone.id));
        const rooms = floors.flatMap((floor) => this.getRoomsByFloorId(floor.id));
        const doors = rooms.flatMap((room) => this.getDoorsByRoomId(room.id));

        const doorStats = {
            open: doors.filter((d) => d.status === 'open').length,
            closed: doors.filter((d) => d.status === 'closed').length,
            locked: doors.filter((d) => d.status === 'locked').length,
        };

        return {
            buildingId: building.id,
            buildingName: building.name,
            shortCode: building.shortCode,
            totalZones: zones.length,
            totalFloors: floors.length,
            totalRooms: rooms.length,
            totalDoors: doors.length,
            doorStats,
        };
    }

    // Zone methods
    public getZonesByBuildingId(buildingId: number): Zone[] {
        return getZonesByBuildingId(buildingId);
    }

    public getZoneByCode(zoneCode: string, buildingId?: number): Zone | undefined {
        return getZoneByCode(zoneCode, buildingId);
    }

    public getZoneById(id: number): Zone | undefined {
        return getZoneById(id);
    }

    // Floor methods
    public getFloorsByZoneId(zoneId: number): Floor[] {
        return getFloorsByZoneId(zoneId);
    }

    public getFloorByCode(floorCode: string, zoneId?: number): Floor | undefined {
        return getFloorByCode(floorCode, zoneId);
    }

    public getFloorById(id: number): Floor | undefined {
        return getFloorById(id);
    }

    // Room methods
    public getRoomsByFloorId(floorId: number): Room[] {
        return getRoomsByFloorId(floorId);
    }

    public getRoomByCode(roomCode: string, floorId?: number): Room | undefined {
        return getRoomByCode(roomCode, floorId);
    }

    public getRoomById(id: number): Room | undefined {
        return getRoomById(id);
    }

    // Door methods
    public getDoorsByRoomId(roomId: number): Door[] {
        return getDoorsByRoomId(roomId);
    }

    public getDoorByCode(doorCode: string): Door | undefined {
        return getDoorByCode(doorCode);
    }

    public getDoorById(id: number): Door | undefined {
        return getDoorById(id);
    }

    // Navigation and location methods
    public getLocationReference(
        buildingId: number,
        zoneId: number,
        floorId: number,
        roomId?: number,
    ): LocationReference | null {
        const building = this.getBuildingById(buildingId);
        const zone = this.getZoneById(zoneId);
        const floor = this.getFloorById(floorId);
        const room = roomId ? this.getRoomById(roomId) : undefined;

        if (!building || !zone || !floor) return null;

        const fullPath = room
            ? `${building.shortCode} > ${zone.zoneCode} > ${floor.floorCode} > ${room.roomCode}`
            : `${building.shortCode} > ${zone.zoneCode} > ${floor.floorCode}`;

        return {
            buildingName: building.name,
            buildingShortCode: building.shortCode,
            zoneName: zone.name,
            zoneCode: zone.zoneCode,
            floorName: floor.name,
            floorCode: floor.floorCode,
            roomName: room?.name,
            roomCode: room?.roomCode,
            fullPath,
        };
    }

    public getNavigationContext(
        buildingId?: number,
        zoneId?: number,
        floorId?: number,
        roomId?: number,
    ): NavigationContext {
        const building = buildingId ? this.getBuildingById(buildingId) : undefined;
        const zone = zoneId ? this.getZoneById(zoneId) : undefined;
        const floor = floorId ? this.getFloorById(floorId) : undefined;
        const room = roomId ? this.getRoomById(roomId) : undefined;

        const breadcrumb: string[] = [];
        if (building) breadcrumb.push(`${building.name} (${building.shortCode})`);
        if (zone) breadcrumb.push(`${zone.name} (${zone.zoneCode})`);
        if (floor) breadcrumb.push(`${floor.name} (${floor.floorCode})`);
        if (room) breadcrumb.push(`${room.name} (${room.roomCode})`);

        return {
            currentBuilding: building,
            currentZone: zone,
            currentFloor: floor,
            currentRoom: room,
            breadcrumb,
        };
    }

    // Search and filter methods
    public searchByCode(code: string): Array<Building | Zone | Floor | Room | Door> {
        const results: Array<Building | Zone | Floor | Room | Door> = [];

        // Search buildings
        const building = this.getBuildingByShortCode(code);
        if (building) results.push(building);

        // Search zones
        const zone = this.getZoneByCode(code);
        if (zone) results.push(zone);

        // Search floors
        const floor = this.getFloorByCode(code);
        if (floor) results.push(floor);

        // Search rooms
        const room = this.getRoomByCode(code);
        if (room) results.push(room);

        // Search doors
        const door = this.getDoorByCode(code);
        if (door) results.push(door);

        return results;
    }

    // Statistics methods
    public getOverallStats(): {
        totalBuildings: number;
        totalZones: number;
        totalFloors: number;
        totalRooms: number;
        totalDoors: number;
        doorStatusBreakdown: DoorStats['byStatus'];
    } {
        const buildings = this.getAllBuildings();
        const zones = this.hierarchy.zones.filter((z) => z.isActive);
        const floors = this.hierarchy.floors.filter((f) => f.isActive);
        const rooms = this.hierarchy.rooms.filter((r) => r.isActive);
        const doors = this.hierarchy.doors.filter((d) => d.isActive);

        const doorStatusBreakdown = {
            open: doors.filter((d) => d.status === 'open').length,
            closed: doors.filter((d) => d.status === 'closed').length,
            locked: doors.filter((d) => d.status === 'locked').length,
            malfunction: doors.filter((d) => d.status === 'malfunction').length,
        };

        return {
            totalBuildings: buildings.length,
            totalZones: zones.length,
            totalFloors: floors.length,
            totalRooms: rooms.length,
            totalDoors: doors.length,
            doorStatusBreakdown,
        };
    }
}

// Export singleton instance
export const geographyService = GeographyService.getInstance();

// Export convenience functions
export const getGeographyService = () => geographyService;
