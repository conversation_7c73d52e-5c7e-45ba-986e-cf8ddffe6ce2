/**
 * Nebular Event Management System - Event Service
 *
 * Service layer for handling all event-related API operations
 * following the Fetchy library patterns and best practices.
 */

import nebularApi from './nebular-api.config';
import type {
    Event,
    CreateEventRequest,
    GetEventsResponse,
    GetEventResponse,
    CreateEventResponse,
    GetEventsQueryParams,
    EventType,
    SystemCode,
    BaseApiResponse,
} from '@/shared/types/nebular-api.types';

/**
 * Event Service Class
 * Provides all CRUD operations for events in the Nebular system
 */
export class EventService {
    private readonly basePath = '/api/v1/events';

    /**
     * Get all events with optional filtering and pagination
     */
    async getEvents(params?: GetEventsQueryParams): Promise<GetEventsResponse> {
        const response = await nebularApi.get<GetEventsResponse>(this.basePath, {
            params: {
                ...params,
                // Ensure proper parameter formatting
                ...(params?.system_code && { system_code: params.system_code }),
                ...(params?.event_type && { event_type: params.event_type }),
                ...(params?.building_id && { building_id: params.building_id }),
                ...(params?.floor_id && { floor_id: params.floor_id }),
                ...(params?.limit && { limit: params.limit }),
                ...(params?.offset && { offset: params.offset }),
                ...(params?.search && { search: params.search }),
                ...(params?.date_from && { date_from: params.date_from }),
                ...(params?.date_to && { date_to: params.date_to }),
            },
        });
        return response.data;
    }

    /**
     * Get a specific event by ID
     */
    async getEvent(id: number): Promise<GetEventResponse> {
        const response = await nebularApi.get<GetEventResponse>(`${this.basePath}/${id}`);
        return response.data;
    }

    /**
     * Create a new event
     */
    async createEvent(eventData: CreateEventRequest): Promise<CreateEventResponse> {
        const response = await nebularApi.post<CreateEventResponse>(this.basePath, {
            body: eventData,
        });
        return response.data;
    }

    /**
     * Update an existing event (using PATCH for partial updates)
     */
    async updateEvent(id: number, eventData: Partial<CreateEventRequest>): Promise<CreateEventResponse> {
        const response = await nebularApi.patch<CreateEventResponse>(`${this.basePath}/${id}`, {
            body: eventData,
        });
        return response.data;
    }

    /**
     * Delete an event
     */
    async deleteEvent(id: number): Promise<BaseApiResponse> {
        const response = await nebularApi.delete<BaseApiResponse>(`${this.basePath}/${id}`);
        return response.data;
    }

    /**
     * Get events by system code
     */
    async getEventsBySystem(
        systemCode: SystemCode,
        params?: Omit<GetEventsQueryParams, 'system_code'>,
    ): Promise<GetEventsResponse> {
        return this.getEvents({ ...params, system_code: systemCode });
    }

    /**
     * Get events by type
     */
    async getEventsByType(
        eventType: EventType,
        params?: Omit<GetEventsQueryParams, 'event_type'>,
    ): Promise<GetEventsResponse> {
        return this.getEvents({ ...params, event_type: eventType });
    }

    /**
     * Get events by building
     */
    async getEventsByBuilding(
        buildingId: number,
        params?: Omit<GetEventsQueryParams, 'building_id'>,
    ): Promise<GetEventsResponse> {
        return this.getEvents({ ...params, building_id: buildingId });
    }

    /**
     * Get events by floor
     */
    async getEventsByFloor(
        floorId: number,
        params?: Omit<GetEventsQueryParams, 'floor_id'>,
    ): Promise<GetEventsResponse> {
        return this.getEvents({ ...params, floor_id: floorId });
    }

    /**
     * Search events by text
     */
    async searchEvents(searchTerm: string, params?: Omit<GetEventsQueryParams, 'search'>): Promise<GetEventsResponse> {
        return this.getEvents({ ...params, search: searchTerm });
    }

    /**
     * Get events within a date range
     */
    async getEventsByDateRange(
        dateFrom: string,
        dateTo: string,
        params?: Omit<GetEventsQueryParams, 'date_from' | 'date_to'>,
    ): Promise<GetEventsResponse> {
        return this.getEvents({ ...params, date_from: dateFrom, date_to: dateTo });
    }

    /**
     * Get upcoming events (events with datetime in the future)
     */
    async getUpcomingEvents(params?: Omit<GetEventsQueryParams, 'date_from'>): Promise<GetEventsResponse> {
        const now = new Date().toISOString();
        return this.getEvents({ ...params, date_from: now });
    }

    /**
     * Get past events (events with datetime in the past)
     */
    async getPastEvents(params?: Omit<GetEventsQueryParams, 'date_to'>): Promise<GetEventsResponse> {
        const now = new Date().toISOString();
        return this.getEvents({ ...params, date_to: now });
    }

    /**
     * Bulk create events
     */
    async createEvents(eventsData: CreateEventRequest[]): Promise<CreateEventResponse[]> {
        const promises = eventsData.map((eventData) => this.createEvent(eventData));
        return Promise.all(promises);
    }

    /**
     * Bulk update events
     */
    async updateEvents(
        updates: Array<{ id: number; data: Partial<CreateEventRequest> }>,
    ): Promise<CreateEventResponse[]> {
        const promises = updates.map(({ id, data }) => this.updateEvent(id, data));
        return Promise.all(promises);
    }

    /**
     * Bulk delete events
     */
    async deleteEvents(ids: number[]): Promise<BaseApiResponse[]> {
        const promises = ids.map((id) => this.deleteEvent(id));
        return Promise.all(promises);
    }

    /**
     * Get event statistics
     */
    async getEventStats(): Promise<{
        total: number;
        bySystem: Record<string, number>;
        byType: Record<string, number>;
        upcoming: number;
        past: number;
    }> {
        // Get all events with a reasonable limit
        const allEvents = await this.getEvents({ limit: 1000 });
        const events = allEvents.events || [];

        const stats = {
            total: allEvents.total_count || events.length,
            bySystem: {} as Record<string, number>,
            byType: {} as Record<string, number>,
            upcoming: 0,
            past: 0,
        };

        const now = new Date();

        events.forEach((event: Event) => {
            // Count by system
            const systemCode = event.systemCode;
            stats.bySystem[systemCode] = (stats.bySystem[systemCode] || 0) + 1;

            // Count by type
            const eventType = event.eventType;
            stats.byType[eventType] = (stats.byType[eventType] || 0) + 1;

            // Count upcoming vs past
            const eventDate = new Date(event.datetime);
            if (eventDate > now) {
                stats.upcoming++;
            } else {
                stats.past++;
            }
        });

        return stats;
    }

    /**
     * Test API connection
     */
    async testConnection(): Promise<boolean> {
        try {
            await this.getEvents({ limit: 1 });
            return true;
        } catch (error) {
            console.error('[Event Service] Connection test failed:', error);
            return false;
        }
    }
}

// Create and export a singleton instance
export const eventService = new EventService();

// Export the class for testing purposes
export default EventService;
