// ---------------------- marker ----------------------
export interface Marker {
    id: number;
    name: string;
    type: 'fire' | 'door' | 'gate' | 'camera' | 'people' | 'person' | 'volumeOn';
    positionX: number;
    positionY: number;
    positionXPercent?: number;
    positionYPercent?: number;
    status: string;
    isAlert: boolean;
    floorId: number;
    zoneId: number;
    buildingId: number;
    title: string;
    subtitle: string;
    description: string;
    zone: string;
    publicAddress: string;
    alertTimestamp: string;
    count?: number;
    source?: string;
}

// ---------------------- marker styles ----------------------
export interface MarkerStyle {
    color: string;
    icon: string;
}

export const markerStyles: Record<Marker['type'], MarkerStyle> = {
    fire: { color: '#ff4444', icon: '🔥' },
    door: { color: '#4CAF50', icon: '🚪' },
    gate: { color: '#FF9800', icon: '🚧' },
    camera: { color: '#2196F3', icon: '📹' },
    people: { color: '#9C27B0', icon: '👤' },
    person: { color: '#9C27B0', icon: '👤' },
    volumeOn: { color: '#607D8B', icon: '📡' },
};

// ---------------------- root ------------------------
export interface Markers {
    markers: Marker[];
}
