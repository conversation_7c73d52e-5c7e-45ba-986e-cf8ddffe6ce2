import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { Event } from '@/shared/types/nebular-api.types';
import { useBuildingStore } from './building.store';
import { eventService } from '@/infrastructure/api/nebular/event.service';
import { config } from '@/shared/config/app.config';
import { useSystemsStore } from './system.store';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type EventsStoreType = EventsState & EventsActions;
let intervalId: NodeJS.Timeout | null = null;

export interface FloorAlertSummary {
    floorId: number;
    floorName: string;
    totalAlerts: number;
    criticalAlerts: number;
    alerts: Event[];
    deviceTypes: {
        fire: number;
        door: number;
        camera: number;
        gate: number;
    };
}

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------
interface EventsState {
    events: Event[];
    isLoading: boolean;
    floorAlerts: FloorAlertSummary[];
    selectedBuildingId: number | null;
    totalAlertsInBuilding: number;
    isInitialized: boolean;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: EventsState = {
    events: [],
    isLoading: false,
    floorAlerts: [],
    selectedBuildingId: null,
    totalAlertsInBuilding: 0,
    isInitialized: false,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface EventsActions {
    loadAllEvents: () => Promise<void>;
    loadAlertEvents: () => Promise<void>;
    loadAlertsByBuilding: (buildingId: number) => Promise<void>;
    getAlertsByFloor: (floorId: number) => Event[];
    getCriticalAlertsByFloor: (floorId: number) => Event[];
    initialize: () => void;
    subscribeToBuildingStore: () => void;
    clearAlerts: () => void;
    startAutoRefresh: () => void;
    stopAutoRefresh: () => void;
    getLatestEventByMarkerId: (markerId: string | number) => Event | undefined;
    getTotalAlertsByBuilding: (buildingId: number) => number;
}

// ---------------------------------------------------
// ---------------------- helpers --------------------
// ---------------------------------------------------
const isCriticalAlert = (event: Event): boolean => event.severity?.toLowerCase() === 'critical';

const isAlert = (event: Event): boolean => isCriticalAlert(event);

// --- helper: count alerts for a building ---
const getTotalAlertsByBuildingHelper = (events: Event[], buildingId: number | null): number => {
    if (!buildingId) return 0;
    return events.filter(
        (e) =>
            e.buildingId === buildingId &&
            e.severity?.toLowerCase() === 'critical' &&
            e.state?.toLowerCase() !== 'resolved' &&
            e.state?.toLowerCase() !== 'close',
    ).length;
};

const groupAlertsByFloor = (events: Event[]): FloorAlertSummary[] => {
    const floorMap = new Map<number, FloorAlertSummary>();

    events.forEach((event) => {
        if (!isAlert(event)) return;

        if (!floorMap.has(event.floorId)) {
            floorMap.set(event.floorId, {
                floorId: event.floorId,
                floorName: `Floor ${event.floorId}`,
                totalAlerts: 0,
                criticalAlerts: 0,
                alerts: [],
                deviceTypes: {
                    fire: 0,
                    door: 0,
                    camera: 0,
                    gate: 0,
                },
            });
        }

        const floorSummary = floorMap.get(event.floorId)!;
        floorSummary.totalAlerts++;
        floorSummary.alerts.push(event);

        if (event.systemCode in floorSummary.deviceTypes) {
            floorSummary.deviceTypes[event.systemCode as keyof typeof floorSummary.deviceTypes]++;
        }

        if (isCriticalAlert(event)) {
            floorSummary.criticalAlerts++;
        }
    });

    return Array.from(floorMap.values()).sort((a, b) => a.floorId - b.floorId);
};

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _eventsStore = (instanceId: string): StateCreator<EventsStoreType> => {
    let initialized = false;
    let unsubscribeBuilding: (() => void) | null = null;

    return (set, get): EventsStoreType => ({
        ...DEFAULT_STATE,

        // Load all events
        loadAllEvents: async () => {
            set({ isLoading: true });
            try {
                const res = await eventService.getEvents();
                const events = res.events || [];
                const alerts = events.filter(isAlert);
                const floorAlerts = groupAlertsByFloor(alerts);

                const selectedBuildingId = get().selectedBuildingId;
                const totalAlertsInBuilding = getTotalAlertsByBuildingHelper(alerts, selectedBuildingId);

                set({
                    events,
                    floorAlerts,
                    totalAlertsInBuilding,
                    isLoading: false,
                });
                logger.info(`eventsStore(${instanceId}): loadAllEvents loaded`, res);
            } catch (error) {
                logger.error(`eventsStore(${instanceId}): loadAllEvents error`, error as Error);
                set({ isLoading: false });
            }
        },

        // Load only alert events
        loadAlertEvents: async () => {
            set({ isLoading: true });
            try {
                const res = await eventService.getEvents();
                const events = res.events || [];
                const alerts = events.filter(isAlert);
                const floorAlerts = groupAlertsByFloor(alerts);

                const selectedBuildingId = get().selectedBuildingId;
                const totalAlertsInBuilding = getTotalAlertsByBuildingHelper(alerts, selectedBuildingId);

                set({
                    events: alerts,
                    floorAlerts,
                    totalAlertsInBuilding,
                    isLoading: false,
                });
                logger.info(`eventsStore(${instanceId}): loadAlertEvents loaded only alerts`, alerts);
            } catch (error) {
                logger.error(`eventsStore(${instanceId}): loadAlertEvents error`, error as Error);
                set({ isLoading: false });
            }
        },

        // Load alerts for specific building
        loadAlertsByBuilding: async (buildingId: number) => {
            set({ isLoading: true });
            try {
                const res = await eventService.getEventsByBuilding(buildingId);
                const events = res.events || [];
                const alerts = events.filter(isAlert);
                const floorAlerts = groupAlertsByFloor(alerts);

                const totalAlertsInBuilding = getTotalAlertsByBuildingHelper(alerts, buildingId);

                set({
                    events: alerts,
                    floorAlerts,
                    selectedBuildingId: buildingId,
                    totalAlertsInBuilding,
                    isLoading: false,
                });

                logger.info(
                    `eventsStore(${instanceId}): loadAlertsByBuilding loaded ${alerts.length} alerts for building ${buildingId}`,
                    { floorAlerts, alerts },
                );
            } catch (error) {
                logger.error(`eventsStore(${instanceId}): loadAlertsByBuilding error`, error as Error);
                set({ isLoading: false });
            }
        },

        // Get alerts for specific floor
        getAlertsByFloor: (floorId: number) => {
            const { floorAlerts } = get();
            const floorSummary = floorAlerts.find((f) => f.floorId === floorId);
            return floorSummary?.alerts || [];
        },

        // Get critical alerts for specific floor
        getCriticalAlertsByFloor: (floorId: number) => {
            const { floorAlerts } = get();
            const floorSummary = floorAlerts.find((f) => f.floorId === floorId);
            return floorSummary?.alerts.filter(isCriticalAlert) || [];
        },

        // Clear all alerts
        clearAlerts: () => {
            set({
                events: [],
                floorAlerts: [],
                selectedBuildingId: null,
                totalAlertsInBuilding: 0,
            });
            logger.info(`eventsStore(${instanceId}): clearAlerts: cleared all alerts`);
        },

        // Initialize store
        initialize: () => {
            const { floorAlerts, isLoading, isInitialized } = get();
            if (!initialized && !isInitialized && floorAlerts.length === 0 && !isLoading) {
                initialized = true;
                set({ isInitialized: true });
                get().loadAlertEvents();
            }
        },

        // Subscribe to building store changes
        subscribeToBuildingStore: () => {
            if (unsubscribeBuilding) unsubscribeBuilding();

            unsubscribeBuilding = useBuildingStore.subscribe(
                (state) => state.selectedBuilding,
                (selectedBuilding) => {
                    if (selectedBuilding && selectedBuilding.id) {
                        get().loadAlertsByBuilding(selectedBuilding.id);
                    } else {
                        get().loadAlertEvents();
                    }
                },
                { fireImmediately: true },
            );
        },

        // --- auto refresh loop ---
        startAutoRefresh: () => {
            console.log('🚀 EventsStore: startAutoRefresh called');
            if (intervalId) {
                console.log('⚠️ EventsStore: Auto refresh already running, skipping');
                return; // already running
            }
            console.log('📡 EventsStore: Starting initial fetch and setting up interval');
            get().loadAllEvents(); // initial fetch
            useSystemsStore.getState().loadSystems(); // initial fetch
            intervalId = setInterval(() => {
                console.log('🔄 EventsStore: Auto refresh interval tick');
                get().loadAllEvents();
                useSystemsStore.getState().loadSystems();
            }, config.nebularApi.autoRefresh); // every 1 min
            console.log(`✅ EventsStore: Auto refresh started with interval ${config.nebularApi.autoRefresh}ms`);
        },

        stopAutoRefresh: () => {
            if (intervalId) {
                clearInterval(intervalId);
                intervalId = null;
            }
        },

        // --- helper to get latest event by marker ---
        getLatestEventByMarkerId: (markerId) => {
            const { events } = get();
            const related = events.filter((e) => e.sourceEventCode === String(markerId));
            if (related.length === 0) return undefined;
            return related.reduce((latest, e) => (new Date(e.datetime) > new Date(latest.datetime) ? e : latest));
        },

        // --- helper: expose building count ---
        getTotalAlertsByBuilding: (buildingId) => {
            const { events } = get();
            return getTotalAlertsByBuildingHelper(events, buildingId);
        },
    });
};

// ----------------------------------------------------------
// ---------------------- store instances -------------------
// ----------------------------------------------------------
export const useEventsStore = create<EventsStoreType>()(
    subscribeWithSelector(devtools(_eventsStore('global'), { name: 'events-store-global' })),
);

export const createEventsStore = (instanceId: string) =>
    create<EventsStoreType>()(
        subscribeWithSelector(devtools(_eventsStore(instanceId), { name: `events-store-${instanceId}` })),
    );
