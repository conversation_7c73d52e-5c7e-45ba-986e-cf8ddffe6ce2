'use client';

import React from 'react';
import { SystemCard } from '@/components/features/alert-dashboard/layout';

export default function SystemCardsDemoPage() {
    return (
        <div className="p-8 bg-gray-50 min-h-screen">
            <div className="max-w-7xl mx-auto">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">System Cards Demo</h1>
                <p className="text-gray-600 mb-8">
                    Demonstration of the SystemCard component with different system types and states.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {/* Fire Alarm System */}
                    <SystemCard
                        title="Fire Alarm System"
                        code="fire"
                        iconName="fire"
                        iconColor="#E87027"
                        metrics={[
                            { key: 'fire_total_devices', label: 'Total devices', value: '200', isAlert: false },
                            { key: 'fire_active_alarms', label: 'Active Alarms', value: '2', isAlert: true },
                        ]}
                    />

                    {/* Access Control System */}
                    <SystemCard
                        title="Access Control"
                        code="access"
                        iconName="door"
                        iconColor="#10BCAD"
                        metrics={[
                            { key: 'access_total_doors', label: 'Total doors', value: '150', isAlert: false },
                            { key: 'access_open_closed', label: 'Open/Closed', value: '6/144', isAlert: false },
                        ]}
                    />

                    {/* CCTV System */}
                    <SystemCard
                        title="CCTV Surveillance"
                        code="cctv"
                        iconName="camera"
                        iconColor="#877BD7"
                        metrics={[
                            { key: 'cctv_total_cameras', label: 'Total cameras', value: '200', isAlert: false },
                            { key: 'cctv_active_incidents', label: 'Active Incidents', value: '2', isAlert: true },
                        ]}
                    />
                </div>
            </div>
        </div>
    );
}
