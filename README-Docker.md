# Docker Setup for Nebular Dashboard

This document provides instructions for running the Nebular Dashboard application using Docker.

docker-compose down && docker-compose --profile production build --no-cache

## Prerequisites

- Docker Desktop installed and running
- Docker Compose (included with Docker Desktop)
- Git (for cloning the repository)

## Getting Started

### 1. Clone the Repository

```bash
<NAME_EMAIL>:LaplaceSoftware/next_nublar_lc.git
cd next_nublar_lc
```

### 2. Build and Run with Docker

Choose one of the following options based on your needs:

## Available Services

### Development Environment (with hot reload)

```bash
# Start development server
docker-compose up nebular-dev

# Or run in detached mode
docker-compose up -d nebular-dev
```

The development server will be available at:

- **Application**: http://localhost:3000
- **Storybook**: http://localhost:6006 (when running storybook service)

### Production Environment

```bash
# Start production server
docker-compose --profile production up nebular-prod

# Or run in detached mode
docker-compose --profile production up -d nebular-prod
```

The production server will be available at:

- **Application**: http://localhost:3001

### Storybook Only

```bash
# Start Storybook server
docker-compose --profile storybook up storybook

# Or run in detached mode
docker-compose --profile storybook up -d storybook
```

Storybook will be available at:

- **Storybook**: http://localhost:6006

## Environment Variables

The application uses environment variables for configuration. There are three main environment files:

### Environment Files

1. **`.env`** - Base environment variables (shared between dev and prod)
2. **`.env.local`** - Local development overrides (used in development only)
3. **`.env.production`** - Production-specific environment variables

### Setting Up Environment Variables

1. **Copy the base environment file:**

   ```bash
   cp .env .env.local
   ```
2. **Update `.env.local` for development:**

   - Modify API URLs to point to your local/development servers
   - Set development-specific feature flags
   - Configure local authentication settings
3. **Update `.env.production` for production:**

   - Set production API URLs and endpoints
   - Configure production authentication credentials
   - Set production feature flags and analytics IDs
   - Update database URLs and external service tokens

### Important Environment Variables

#### Required for Build (NEXT_PUBLIC_*)

These variables are embedded into the client-side bundle during build:

- `NEXT_PUBLIC_API_BASE_URL` - Main API endpoint
- `NEXT_PUBLIC_NEBULAR_API_URL` - Nebular API endpoint
- `NEXT_PUBLIC_APP_NAME` - Application name
- `NEXT_PUBLIC_FEATURE_*` - Feature flags
- `NEXT_PUBLIC_GOOGLE_ANALYTICS_ID` - Analytics tracking ID

#### Runtime Variables

These are used server-side and in API routes:

- `DATABASE_URL` - Database connection string
- `NEXTAUTH_SECRET` - Authentication secret
- `NEXTAUTH_URL` - Authentication callback URL

### Docker Environment Handling

#### Development

```bash
# Uses .env and .env.local files
docker-compose up nebular-dev
```

#### Production

```bash
# Uses .env and .env.production files
docker-compose --profile production up nebular-prod
```

#### Building with Environment Variables

For production builds, you can pass build arguments:

```bash
docker build \
  --build-arg NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com \
  --build-arg NEXT_PUBLIC_APP_NAME="Nebular Dashboard" \
  -t nebular-dashboard .
```

### Security Notes

⚠️ **Important Security Considerations:**

1. **Never commit sensitive data** to `.env.production` or any environment file
2. **Use secrets management** in production (AWS Secrets Manager, Azure Key Vault, etc.)
3. **NEXT_PUBLIC_* variables** are exposed to the client-side - never put secrets in them
4. **Rotate secrets regularly** in production environments
5. **Use different databases** for development and production

### Environment Variable Validation

The application validates required environment variables on startup. If critical variables are missing, the application will fail to start with clear error messages.

- **Dockerfile**: Multi-stage production build with optimized image size
- **Dockerfile.dev**: Development build with all dependencies and hot reload support
- **docker-compose.yml**: Orchestrates different services (dev, prod, storybook)
- **.dockerignore**: Excludes unnecessary files from build context

## Common Commands

### Build Images

```bash
# Build development image
docker-compose build nebular-dev

# Build production image
docker-compose build nebular-prod

# Build all images
docker-compose build
```

### View Logs

```bash
# View logs for development service
docker-compose logs nebular-dev

# Follow logs in real-time
docker-compose logs -f nebular-dev
```

### Stop Services

```bash
# Stop all running services
docker-compose down

# Stop and remove volumes
docker-compose down -v
```

### Clean Up

```bash
# Remove all containers, networks, and images
docker-compose down --rmi all

# Remove unused Docker resources
docker system prune -a
```

## Development Workflow

1. **Start development environment**:

   ```bash
   docker-compose up nebular-dev
   ```
2. **Make code changes**: Files are mounted as volumes, so changes will trigger hot reload
3. **View application**: Open http://localhost:3000 in your browser
4. **Run Storybook** (optional):

   ```bash
   docker-compose --profile storybook up storybook
   ```

## Production Deployment

1. **Set up production environment variables:**

   ```bash
   # Update .env.production with your production values
   nano .env.production
   ```
2. **Build and run production container:**

   ```bash
   docker-compose --profile production up --build nebular-prod
   ```
3. **Or build with specific environment variables:**

   ```bash
   docker build \
     --build-arg NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com \
     --build-arg NEXT_PUBLIC_APP_NAME="Nebular Dashboard" \
     -t nebular-dashboard .

   docker run -p 3001:3000 --env-file .env.production nebular-dashboard
   ```
4. **Access application**: Open http://localhost:3001 in your browser

## Troubleshooting

### Port Conflicts

If you encounter port conflicts, you can modify the ports in `docker-compose.yml`:

```yaml
ports:
  - "3001:3000"  # Change 3001 to any available port
```

### Permission Issues (Linux/macOS)

If you encounter permission issues with mounted volumes:

```bash
# Fix ownership
sudo chown -R $USER:$USER .
```

### Container Won't Start

Check the logs for detailed error information:

```bash
docker-compose logs nebular-dev
```

### Clear Cache and Rebuild

```bash
# Remove containers and rebuild
docker-compose down
docker-compose build --no-cache
docker-compose up
```

## Environment Variables

You can create environment-specific files:

- `.env.local` - Local development overrides
- `.env.production` - Production environment variables

These files are automatically excluded from the Docker build context via `.dockerignore`.

## Performance Notes

- The development image includes all dependencies and development tools
- The production image uses multi-stage builds for smaller size and better security
- Hot reload is enabled in development mode with volume mounting
- Production builds use Next.js standalone output for optimal performance

## Deployment Workflow

### Development Deployment

```bash
# 1. Clone the repository
<NAME_EMAIL>:LaplaceSoftware/next_nublar_lc.git
cd next_nublar_lc

# 2. Build and start development environment
docker-compose build nebular-dev
docker-compose up -d nebular-dev

# 3. Access the application at http://localhost:3000
```

### Production Deployment

```bash
# 1. Clone the repository
<NAME_EMAIL>:LaplaceSoftware/next_nublar_lc.git
cd next_nublar_lc

# 2. Build and start production environment
docker-compose build nebular-prod
docker-compose --profile production up -d nebular-prod

# 3. Access the application at http://localhost:3001
```

### Making Changes and Deploying Updates

```bash
# 1. Make your code changes
# 2. Commit and push changes
git add .
git commit -m "Your commit message"
git push origin main

# 3. On deployment server, pull latest changes
git pull origin main

# 4. Rebuild and restart containers
docker-compose down
docker-compose build --no-cache
docker-compose up -d nebular-dev  # or nebular-prod for production
```
