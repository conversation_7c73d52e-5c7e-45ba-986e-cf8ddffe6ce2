# System Metrics Calculation - From Static Markers to API Integration

## Overview

This document explains the current implementation of system metrics calculation and the planned migration from static marker-based calculations to direct API integration.

## Current Implementation

### Static Marker-Based Calculation

Currently, the system calculates total metrics (devices, doors, cameras, barriers) by counting static markers from JSON files located in `/public/markers/`. This approach was implemented as a temporary solution during development.

#### Key Components:

1. **Marker Store** (`src/stores/markers.store.ts`)
   - Loads marker data from static JSON files
   - Calculates totals by counting markers of specific types
   - Properties: `totalDevices`, `totalDoors`, `totalCameras`, `totalBarriers`

2. **Merge Function** (`src/infrastructure/api/systems/marker-based-systems.ts`)
   - `mergeSystemsWithMarkerMetrics()` function
   - Combines API system data with marker-calculated totals
   - Updates only total count metrics while preserving other API data

3. **System Overview Component** (`src/components/features/alert-dashboard/layout/SystemOverview/SystemOverview.tsx`)
   - Uses feature flag `USE_MARKER_BASED_METRICS` to control data source
   - Displays merged data when flag is enabled

### Current Metric Mapping:

| System Type | Metric Key | Label | Source |
|-------------|------------|-------|---------|
| Fire Alarm | `fire_total_devices` | "Total Devices" | Static markers |
| Access Control | `access_total_doors` | "Total Doors" | Static markers |
| CCTV | `cctv_total_cameras` | "Total Cameras" | Static markers |
| Gate Barriers | `gate_total_barriers` | "Total Barriers" | Static markers |
| PA System | `pa_total_devices` | "Total Devices" | Static markers |
| Presence Detection | `presence_total_devices` | "Total Devices" | Static markers |

## API Structure

### Current API Response Format:

```typescript
interface System {
  id: string;
  title: string;
  code: string; // New field for system identification
  iconName: string;
  iconColor: string;
  metrics: Metric[];
}

interface Metric {
  key: string; // Specific keys like "fire_total_devices"
  label: string; // User-friendly labels like "Total Devices"
  value: number;
  isAlert: boolean;
  textAlign?: 'left' | 'center' | 'right';
}
```

## Future Migration Plan

### Phase 1: API Enhancement (Planned)
- Enhance the backend API to include real-time total counts
- API should provide accurate metrics directly from the database
- Eliminate dependency on static marker files

### Phase 2: Frontend Migration
1. **Remove Static Dependencies**
   - Remove marker-based calculation logic
   - Remove `mergeSystemsWithMarkerMetrics()` function
   - Clean up marker store total calculation methods

2. **Update Components**
   - Remove feature flag `USE_MARKER_BASED_METRICS`
   - Use API data directly in SystemOverview component
   - Ensure all components use `metric.label` for display

3. **Testing & Validation**
   - Verify metrics accuracy against real system data
   - Ensure UI displays correct values
   - Performance testing for real-time updates

### Phase 3: Real-time Updates (Future)
- Implement WebSocket or polling for real-time metric updates
- Add caching strategies for performance optimization
- Consider implementing metric history tracking

## Benefits of API-Based Approach

1. **Accuracy**: Real-time data from actual systems
2. **Scalability**: No dependency on static files
3. **Maintainability**: Single source of truth
4. **Performance**: Reduced client-side calculations
5. **Flexibility**: Easy to add new metrics or systems

## Migration Checklist

- [ ] Backend API provides total count metrics
- [ ] API response includes all required metric keys and labels
- [ ] Remove `mergeSystemsWithMarkerMetrics()` function
- [ ] Update SystemOverview to use API data directly
- [ ] Remove `USE_MARKER_BASED_METRICS` feature flag
- [ ] Clean up marker store total calculations
- [ ] Update tests and documentation
- [ ] Performance testing with real API data

## Technical Notes

- Current implementation uses system `code` field for identification
- Metric `key` field uses specific naming convention (e.g., `fire_total_devices`)
- Metric `label` field provides user-friendly display names
- SystemCard component displays metrics using `label` field for better UX

## Related Files

- `src/stores/markers.store.ts` - Marker data management
- `src/infrastructure/api/systems/marker-based-systems.ts` - Merge logic
- `src/components/features/alert-dashboard/layout/SystemOverview/SystemOverview.tsx` - Main component
- `src/components/features/alert-dashboard/layout/SystemCard/components/SystemCardMetrics.tsx` - Metric display
- `src/infrastructure/api/systems/types.ts` - Type definitions