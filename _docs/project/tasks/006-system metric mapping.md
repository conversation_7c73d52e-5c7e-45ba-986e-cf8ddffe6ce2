# Task1 : total system metric mapping

Make analysis how system overview and metric load in UI when call api

```
{
    "systems": [
        {
            "title": "Fire Alarm System",
            "iconName": "fire",
            "iconColor": "#E87027",
            "metrics": [
                {
                    "key": "Total devices",
                    "value": "1",
                    "isAlert": false,
                    "textAlign": "start"
                },
                {
                    "key": "Active Alarms",
                    "value": "4",
                    "isAlert": true,
                    "textAlign": "end"
                }
            ]
        },
        {
            "title": "Access Control",
            "iconName": "door",
            "iconColor": "#10BCAD",
            "metrics": [
                {
                    "key": "Total doors",
                    "value": "3",
                    "isAlert": false,
                    "textAlign": "start"
                },
                {
                    "key": "Open/Closed",
                    "value": "0/3",
                    "isAlert": false,
                    "textAlign": "end"
                }
            ]
        },
        {
            "title": "CCTV Surveillance",
            "iconName": "camera",
            "iconColor": "#877BD7",
            "metrics": [
                {
                    "key": "Total cameras",
                    "value": "3",
                    "isAlert": false,
                    "textAlign": "start"
                },
                {
                    "key": "Active Incidents",
                    "value": "2",
                    "isAlert": true,
                    "textAlign": "end"
                }
            ]
        },
        {
            "title": "Gate Barrier",
            "iconName": "gateBarrier",
            "iconColor": "#5C9DD5",
            "metrics": [
                {
                    "key": "Total barriers",
                    "value": "6",
                    "isAlert": false,
                    "textAlign": "start"
                },
                {
                    "key": "Unauthorized attempts",
                    "value": "4",
                    "isAlert": true,
                    "textAlign": "end"
                }
            ]
        },
        {
            "title": "Public Address System",
            "iconName": "volume-up",
            "iconColor": "#6f42c1",
            "metrics": [
                {
                    "key": "Total devices",
                    "value": "0",
                    "isAlert": false,
                    "textAlign": "start"
                },
                {
                    "key": "Active Critical",
                    "value": "1",
                    "isAlert": true,
                    "textAlign": "end"
                }
            ]
        },
        {
            "title": "Presence Detection System",
            "iconName": "user-check",
            "iconColor": "#17a2b8",
            "metrics": [
                {
                    "key": "Total devices",
                    "value": "0",
                    "isAlert": false,
                    "textAlign": "start"
                },
                {
                    "key": "Active Critical",
                    "value": "2",
                    "isAlert": true,
                    "textAlign": "end"
                }
            ]
        }
    ]
}

```

i want make mapping some of metrics like total doors , total devices , total Barries , total cameras ,total  fire devices
to be based on markers grouping per system (marker type) and  selected building

this would be temporary solution based on configuration .env , src/shared/config/app.config.ts

 in future no mapping will be direct from api
because markers is static in json file now we need to temp solution indicate totals metrics correct based on static data

todo this need make anylysis to

- src/components/features/alert-dashboard/layout/SystemOverview/SystemOverview.tsx
- marker in src/components/features/2D-floor-plan/FloorPlan.tsx
- marker store :/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/next_nublar_lc/src/stores/markers.store.ts
  business related to marker counter should be in marker counter should be in /stores/markers.store.ts
- marker json sample

  - in selected building all marker  :building - public/markers/b1-f1-all.json
  - in selected floor marker: /public/markers/b1-f1.json

  # Task 2 : maker type indicator counter

  then beside alert notification counter in floor plan need all icons type indicator on selected floor in horizontal row
  this icon for door and count, gate and count , camera and count  , public address and count , fire and count

  marker counters business should be in /stores/markers.store.ts
