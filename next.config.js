/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  experimental: {
    // Enable turbopack for development
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },
  images: {
    domains: ['localhost'],
  },
  env: {
    // Make environment variables available to the client
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
    NEXT_PUBLIC_API_TIMEOUT: process.env.NEXT_PUBLIC_API_TIMEOUT,
    NEXT_PUBLIC_API_RETRY_ATTEMPTS: process.env.NEXT_PUBLIC_API_RETRY_ATTEMPTS,
    NEXT_PUBLIC_API_RETRY_DELAY: process.env.NEXT_PUBLIC_API_RETRY_DELAY,
    NEXT_PUBLIC_NEBULAR_API_BASE_URL: process.env.NEXT_PUBLIC_NEBULAR_API_BASE_URL,
    NEXT_PUBLIC_NEBULAR_API_TIMEOUT: process.env.NEXT_PUBLIC_NEBULAR_API_TIMEOUT,
    NEXT_PUBLIC_NEBULAR_API_RETRY_ATTEMPTS: process.env.NEXT_PUBLIC_NEBULAR_API_RETRY_ATTEMPTS,
    NEXT_PUBLIC_NEBULAR_API_RETRY_DELAY: process.env.NEXT_PUBLIC_NEBULAR_API_RETRY_DELAY,
    NEXT_PUBLIC_NEBULAR_AUTO_REFRESH: process.env.NEXT_PUBLIC_NEBULAR_AUTO_REFRESH,
    NEXT_PUBLIC_ENABLE_MOCK_DATA: process.env.NEXT_PUBLIC_ENABLE_MOCK_DATA,
    NEXT_PUBLIC_ENABLE_NEW_UI: process.env.NEXT_PUBLIC_ENABLE_NEW_UI,
    NEXT_PUBLIC_ENABLE_LOGGING: process.env.NEXT_PUBLIC_ENABLE_LOGGING,
    NEXT_PUBLIC_ENABLE_DEBUG_TOOLS: process.env.NEXT_PUBLIC_ENABLE_DEBUG_TOOLS,
    NEXT_PUBLIC_USE_MARKER_BASED_METRICS: process.env.NEXT_PUBLIC_USE_MARKER_BASED_METRICS,
    NEXT_PUBLIC_APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION,
  },
}

module.exports = nextConfig