# ---- Base image ----
FROM node:20-alpine AS base

# ---- Dependencies ----
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci --ignore-scripts

# ---- Build stage ----
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Copy environment file for production
COPY .env.production .env.production

# Define build arguments that can be passed from docker-compose
ARG NEXT_PUBLIC_API_BASE_URL
ARG NEXT_PUBLIC_NEBULAR_API_BASE_URL

# Set environment variables from build args for the build process
ENV NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL}
ENV NEXT_PUBLIC_NEBULAR_API_BASE_URL=${NEXT_PUBLIC_NEBULAR_API_BASE_URL}

# Build the application
RUN npm run build

# ---- Production image ----
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production

# Add user
RUN addgroup --system --gid 1001 nodejs \
  && adduser --system --uid 1001 nextjs

# Public assets
COPY --from=builder /app/public ./public

# Next.js build output (standalone mode recommended)
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]