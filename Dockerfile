# ---- Base image ----
FROM node:20-alpine AS base
RUN apk add --no-cache libc6-compat

WORKDIR /app

# ---- Dependencies ----
FROM base AS deps
# Copy only package files for better caching
COPY package.json package-lock.json* ./
RUN npm ci --ignore-scripts

# ---- Build stage ----
FROM base AS builder
WORKDIR /app

# Reuse node_modules from deps layer
COPY --from=deps /app/node_modules ./node_modules

# Copy app source
COPY . .

# Copy production env file (make sure it's not gitignored if needed)
COPY .env.production .env.production

# Pass build args (can be set via docker-compose or CI/CD)
ARG NEXT_PUBLIC_API_BASE_URL
ARG NEXT_PUBLIC_NEBULAR_API_BASE_URL

# Set environment variables during build
ENV NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL}
ENV NEXT_PUBLIC_NEBULAR_API_BASE_URL=${NEXT_PUBLIC_NEBULAR_API_BASE_URL}

# Recommended: enable standalone build for smaller image
RUN npm run build:docker && npm prune --omit=dev

# ---- Production image ----
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Add a non-root user
RUN addgroup --system --gid 1001 nodejs \
  && adduser --system --uid 1001 nextjs

# Copy public assets
COPY --from=builder /app/public ./public

# Copy Next.js standalone output
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# (Optional) If you need package.json for runtime
COPY --from=builder /app/package.json ./

USER nextjs

EXPOSE 4000
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Start Next.js standalone server
CMD ["node", "server.js"]
