# Use the official Node.js 20 image as the base image
FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN npm ci --ignore-scripts

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Next.js collects completely anonymous telemetry data about general usage.
# Learn more here: https://nextjs.org/telemetry
# Uncomment the following line in case you want to disable telemetry during the build.
# ENV NEXT_TELEMETRY_DISABLED 1

# Build arguments for environment variables that need to be available at build time
ARG NEXT_PUBLIC_API_BASE_URL
ARG NEXT_PUBLIC_NEBULAR_API_BASE_URL
ARG NEXT_PUBLIC_API_TIMEOUT
ARG NEXT_PUBLIC_API_RETRY_ATTEMPTS
ARG NEXT_PUBLIC_API_RETRY_DELAY
ARG NEXT_PUBLIC_NEBULAR_API_TIMEOUT
ARG NEXT_PUBLIC_NEBULAR_API_RETRY_ATTEMPTS
ARG NEXT_PUBLIC_NEBULAR_API_RETRY_DELAY
ARG NEXT_PUBLIC_ENABLE_MOCK_DATA
ARG NEXT_PUBLIC_NEBULAR_AUTO_REFRESH
ARG NEXT_PUBLIC_ENABLE_NEW_UI
ARG NEXT_PUBLIC_ENABLE_LOGGING
ARG NEXT_PUBLIC_ENABLE_DEBUG_TOOLS
ARG NEXT_PUBLIC_USE_MARKER_BASED_METRICS
ARG NEXT_PUBLIC_TOKEN_STORAGE_KEY
ARG NEXT_PUBLIC_REFRESH_TOKEN_KEY
ARG NEXT_PUBLIC_SESSION_TIMEOUT
ARG NEXT_PUBLIC_DEFAULT_LANGUAGE
ARG NEXT_PUBLIC_SUPPORTED_LANGUAGES
ARG NEXT_PUBLIC_APP_NAME
ARG NEXT_PUBLIC_APP_VERSION
ARG NEXT_PUBLIC_BUILD_DATE

# Set environment variables for build
ENV NEXT_PUBLIC_API_BASE_URL=$NEXT_PUBLIC_API_BASE_URL
ENV NEXT_PUBLIC_NEBULAR_API_BASE_URL=$NEXT_PUBLIC_NEBULAR_API_BASE_URL
ENV NEXT_PUBLIC_API_TIMEOUT=$NEXT_PUBLIC_API_TIMEOUT
ENV NEXT_PUBLIC_API_RETRY_ATTEMPTS=$NEXT_PUBLIC_API_RETRY_ATTEMPTS
ENV NEXT_PUBLIC_API_RETRY_DELAY=$NEXT_PUBLIC_API_RETRY_DELAY
ENV NEXT_PUBLIC_NEBULAR_API_TIMEOUT=$NEXT_PUBLIC_NEBULAR_API_TIMEOUT
ENV NEXT_PUBLIC_NEBULAR_API_RETRY_ATTEMPTS=$NEXT_PUBLIC_NEBULAR_API_RETRY_ATTEMPTS
ENV NEXT_PUBLIC_NEBULAR_API_RETRY_DELAY=$NEXT_PUBLIC_NEBULAR_API_RETRY_DELAY
ENV NEXT_PUBLIC_ENABLE_MOCK_DATA=$NEXT_PUBLIC_ENABLE_MOCK_DATA
ENV NEXT_PUBLIC_NEBULAR_AUTO_REFRESH=$NEXT_PUBLIC_NEBULAR_AUTO_REFRESH
ENV NEXT_PUBLIC_ENABLE_NEW_UI=$NEXT_PUBLIC_ENABLE_NEW_UI
ENV NEXT_PUBLIC_ENABLE_LOGGING=$NEXT_PUBLIC_ENABLE_LOGGING
ENV NEXT_PUBLIC_ENABLE_DEBUG_TOOLS=$NEXT_PUBLIC_ENABLE_DEBUG_TOOLS
ENV NEXT_PUBLIC_USE_MARKER_BASED_METRICS=$NEXT_PUBLIC_USE_MARKER_BASED_METRICS
ENV NEXT_PUBLIC_TOKEN_STORAGE_KEY=$NEXT_PUBLIC_TOKEN_STORAGE_KEY
ENV NEXT_PUBLIC_REFRESH_TOKEN_KEY=$NEXT_PUBLIC_REFRESH_TOKEN_KEY
ENV NEXT_PUBLIC_SESSION_TIMEOUT=$NEXT_PUBLIC_SESSION_TIMEOUT
ENV NEXT_PUBLIC_DEFAULT_LANGUAGE=$NEXT_PUBLIC_DEFAULT_LANGUAGE
ENV NEXT_PUBLIC_SUPPORTED_LANGUAGES=$NEXT_PUBLIC_SUPPORTED_LANGUAGES
ENV NEXT_PUBLIC_APP_NAME=$NEXT_PUBLIC_APP_NAME
ENV NEXT_PUBLIC_APP_VERSION=$NEXT_PUBLIC_APP_VERSION
ENV NEXT_PUBLIC_BUILD_DATE=$NEXT_PUBLIC_BUILD_DATE

RUN npm run build:docker

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
# Uncomment the following line in case you want to disable telemetry during runtime.
# ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
# set hostname to localhost
ENV HOSTNAME "0.0.0.0"

# server.js is created by next build from the standalone output
# https://nextjs.org/docs/pages/api-reference/next-config-js/output
CMD ["node", "server.js"]