# Development Dockerfile for hot reload and development features
FROM node:20-alpine

# Install dependencies for development
RUN apk add --no-cache libc6-compat

WORKDIR /app

# Copy package files
COPY package.json package-lock.json* ./

# Install all dependencies (including dev dependencies)
RUN npm ci

# Copy source code
COPY . .

# Expose ports for Next.js and Storybook
EXPOSE 3000 6006

# Default command (can be overridden in docker-compose)
CMD ["npm", "run", "dev"]