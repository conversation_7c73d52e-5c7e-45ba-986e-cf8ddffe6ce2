networks:
  lp-apps:
    external: true

services:
  # Development service with hot reload
  nebular-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "4000:3000"
      - "6006:6006"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    env_file:
      - .env
      - .env.local
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
    command: npm run dev
    stdin_open: true
    tty: true

  # Production service
  nebular-prod:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NEXT_PUBLIC_API_BASE_URL: ${NEXT_PUBLIC_API_BASE_URL}
        NEXT_PUBLIC_NEBULAR_API_BASE_URL: ${NEXT_PUBLIC_NEBULAR_API_BASE_URL}
        # add more NEXT_PUBLIC_* args if you need them at build time
    ports:
      - "4000:3000"
    env_file:
      - .env.production   # production vars (baked at build + runtime)
    environment:
      - NODE_ENV=production
    profiles:
      - production
    networks:
      - lp-apps

  # Storybook service
  storybook:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "6006:6006"
    volumes:
      - .:/app
      - /app/node_modules
    env_file:
      - .env
      - .env.local
    environment:
      - NODE_ENV=development
    command: npm run storybook
    profiles:
      - storybook
    networks:
      - lp-apps