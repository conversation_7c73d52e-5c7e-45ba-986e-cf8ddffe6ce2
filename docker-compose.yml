networks:
  lp-apps:
    external: true

services:
  # Development service with hot reload
  nebular-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
      - "6006:6006"  # Storybook port
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    env_file:
      - .env
      - .env.local
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
    command: npm run dev
    stdin_open: true
    tty: true

  # Production service
  nebular-prod:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_NEBULAR_API_BASE_URL=${NEXT_PUBLIC_NEBULAR_API_BASE_URL}
        - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL}
        - NEXT_PUBLIC_API_TIMEOUT=${NEXT_PUBLIC_API_TIMEOUT}
        - NEXT_PUBLIC_API_RETRY_ATTEMPTS=${NEXT_PUBLIC_API_RETRY_ATTEMPTS}
        - NEXT_PUBLIC_API_RETRY_DELAY=${NEXT_PUBLIC_API_RETRY_DELAY}
        - NEXT_PUBLIC_NEBULAR_API_TIMEOUT=${NEXT_PUBLIC_NEBULAR_API_TIMEOUT}
        - NEXT_PUBLIC_NEBULAR_API_RETRY_ATTEMPTS=${NEXT_PUBLIC_NEBULAR_API_RETRY_ATTEMPTS}
        - NEXT_PUBLIC_NEBULAR_API_RETRY_DELAY=${NEXT_PUBLIC_NEBULAR_API_RETRY_DELAY}
        - NEXT_PUBLIC_ENABLE_MOCK_DATA=${NEXT_PUBLIC_ENABLE_MOCK_DATA}
        - NEXT_PUBLIC_NEBULAR_AUTO_REFRESH=${NEXT_PUBLIC_NEBULAR_AUTO_REFRESH}
        - NEXT_PUBLIC_ENABLE_NEW_UI=${NEXT_PUBLIC_ENABLE_NEW_UI}
        - NEXT_PUBLIC_ENABLE_LOGGING=${NEXT_PUBLIC_ENABLE_LOGGING}
        - NEXT_PUBLIC_ENABLE_DEBUG_TOOLS=${NEXT_PUBLIC_ENABLE_DEBUG_TOOLS}
        - NEXT_PUBLIC_USE_MARKER_BASED_METRICS=${NEXT_PUBLIC_USE_MARKER_BASED_METRICS}
        - NEXT_PUBLIC_TOKEN_STORAGE_KEY=${NEXT_PUBLIC_TOKEN_STORAGE_KEY}
        - NEXT_PUBLIC_REFRESH_TOKEN_KEY=${NEXT_PUBLIC_REFRESH_TOKEN_KEY}
        - NEXT_PUBLIC_SESSION_TIMEOUT=${NEXT_PUBLIC_SESSION_TIMEOUT}
        - NEXT_PUBLIC_DEFAULT_LANGUAGE=${NEXT_PUBLIC_DEFAULT_LANGUAGE}
        - NEXT_PUBLIC_SUPPORTED_LANGUAGES=${NEXT_PUBLIC_SUPPORTED_LANGUAGES}
        - NEXT_PUBLIC_APP_NAME=${NEXT_PUBLIC_APP_NAME}
        - NEXT_PUBLIC_APP_VERSION=${NEXT_PUBLIC_APP_VERSION}
        - NEXT_PUBLIC_BUILD_DATE=${NEXT_PUBLIC_BUILD_DATE}
    ports:
      - "4000:3000"
    env_file:
      - .env
      - .env.production
    environment:
      - NODE_ENV=production
    profiles:
      - production

  # Storybook service
  storybook:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "6006:6006"
    volumes:
      - .:/app
      - /app/node_modules
    env_file:
      - .env
      - .env.local
    environment:
      - NODE_ENV=development
    command: npm run storybook
    profiles:
      - storybook